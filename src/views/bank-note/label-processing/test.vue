<template>
  <div class="test-page">
    <h2>钱币标签处理功能测试页面</h2>
    
    <!-- 基础组件测试 -->
    <el-card class="test-card">
      <template #header>
        <h3>基础组件测试</h3>
      </template>
      
      <div class="test-section">
        <h4>Element Plus 组件测试</h4>
        <el-button type="primary">主要按钮</el-button>
        <el-button type="success">成功按钮</el-button>
        <el-button type="warning">警告按钮</el-button>
        <el-button type="danger">危险按钮</el-button>
      </div>
      
      <div class="test-section">
        <h4>图标测试</h4>
        <el-icon size="24"><UploadFilled /></el-icon>
        <el-icon size="24"><DocumentChecked /></el-icon>
        <el-icon size="24"><CircleCheckFilled /></el-icon>
      </div>
      
      <div class="test-section">
        <h4>上传组件测试</h4>
        <el-upload
          class="upload-demo"
          drag
          :auto-upload="false"
          accept=".zip"
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            拖拽文件到此处或<em>点击上传</em>
          </div>
        </el-upload>
      </div>
    </el-card>

    <!-- API 测试 -->
    <el-card class="test-card">
      <template #header>
        <h3>API 连接测试</h3>
      </template>
      
      <div class="test-section">
        <el-button type="primary" @click="testHealthCheck" :loading="healthChecking">
          测试健康检查接口
        </el-button>
        <div v-if="healthResult" class="test-result">
          <pre>{{ JSON.stringify(healthResult, null, 2) }}</pre>
        </div>
      </div>
    </el-card>

    <!-- 主要组件测试 -->
    <el-card class="test-card">
      <template #header>
        <h3>标签处理组件测试</h3>
      </template>
      
      <div class="test-section">
        <LabelArchiveUpload />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { UploadFilled, DocumentChecked, CircleCheckFilled } from '@element-plus/icons-vue';
import LabelArchiveUpload from './components/LabelArchiveUpload.vue';
import request from '@/utils/request';

// 响应式数据
const healthChecking = ref(false);
const healthResult = ref(null);

// 测试健康检查接口
const testHealthCheck = async () => {
  healthChecking.value = true;
  healthResult.value = null;
  
  try {
    const response = await request.get('/labels/health');
    healthResult.value = response.data;
    ElMessage.success('健康检查接口连接成功');
  } catch (error) {
    console.error('健康检查失败:', error);
    healthResult.value = {
      error: error.message,
      status: 'FAILED'
    };
    ElMessage.error('健康检查接口连接失败: ' + error.message);
  } finally {
    healthChecking.value = false;
  }
};
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.test-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.test-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.test-result {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}

.upload-demo {
  width: 100%;
}

.el-button {
  margin-right: 10px;
}

.el-icon {
  margin-right: 10px;
}
</style>
