import request from '@/utils/request';

/**
 * 批量处理钱币标签图片压缩包
 * @param {File} file - ZIP压缩包文件
 * @returns {Promise} 处理结果
 */
export async function processLabelArchive(file) {
  const formData = new FormData();
  formData.append('file', file);

  const res = await request.post('/labels/process-archive', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 300000 // 5分钟超时
  });

  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取处理历史记录（如果需要的话）
 * @param {Object} params - 查询参数
 * @returns {Promise} 历史记录列表
 */
export async function getProcessingHistory(params) {
  const res = await request.get('/labels/processing-history', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
