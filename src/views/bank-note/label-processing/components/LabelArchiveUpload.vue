<template>
  <div class="label-archive-upload">
    <el-card class="upload-card">
      <template #header>
        <div class="card-header">
          <h3>钱币标签图片批量处理</h3>
          <p class="description">
            上传包含钱币标签图片的ZIP压缩包，系统将自动识别二维码或OCR文本提取送评单号，并更新对应钱币的正反面图片。
          </p>
        </div>
      </template>

      <!-- 上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="upload-dragger"
          drag
          :auto-upload="false"
          :limit="1"
          accept=".zip"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :file-list="fileList"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将ZIP压缩包拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              <p>只能上传.zip格式的压缩包文件，且不超过100MB</p>
              <p>图片命名规则：[前缀]_[编号].jpg（如：abcd_01.jpg, abcd_02.jpg）</p>
              <p>奇数编号为正面，偶数编号为反面</p>
            </div>
          </template>
        </el-upload>

        <!-- 处理按钮 -->
        <div class="action-buttons">
          <el-button
            type="primary"
            size="large"
            :disabled="!canProcess"
            :loading="processing"
            @click="startProcessing"
          >
            <el-icon v-if="!processing"><DocumentChecked /></el-icon>
            {{ processing ? '正在处理，请稍候...' : '开始处理' }}
          </el-button>
          
          <el-button
            v-if="fileList.length > 0"
            size="large"
            @click="clearFiles"
          >
            清空文件
          </el-button>
        </div>
      </div>

      <!-- 处理进度 -->
      <div v-if="processing" class="progress-section">
        <el-progress
          :percentage="progressPercentage"
          :status="progressStatus"
          :stroke-width="8"
        />
        <p class="progress-text">{{ progressText }}</p>
      </div>

      <!-- 处理结果 -->
      <div v-if="result" class="result-section">
        <el-alert
          :title="resultTitle"
          :type="resultType"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="result-summary">
              <p><strong>处理统计：</strong></p>
              <ul>
                <li>总配对数：{{ result.totalPairs }}</li>
                <li>成功处理：{{ result.successCount }}</li>
                <li>失败处理：{{ result.failureCount }}</li>
                <li>处理耗时：{{ formatTime(result.processingTime) }}</li>
              </ul>
            </div>
          </template>
        </el-alert>

        <!-- 失败详情 -->
        <div v-if="result.failures && result.failures.length > 0" class="failure-details">
          <el-collapse v-model="activeFailures">
            <el-collapse-item title="查看失败详情" name="failures">
              <el-table :data="result.failures" stripe>
                <el-table-column prop="prefix" label="图片前缀" width="120" />
                <el-table-column prop="error" label="失败原因" />
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 重新处理按钮 -->
        <div class="result-actions">
          <el-button type="primary" @click="resetForm">
            处理新的压缩包
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { UploadFilled, DocumentChecked } from '@element-plus/icons-vue';
import { processLabelArchive } from '../api';

// 响应式数据
const uploadRef = ref();
const fileList = ref([]);
const processing = ref(false);
const result = ref(null);
const progressPercentage = ref(0);
const progressStatus = ref('');
const progressText = ref('');
const activeFailures = ref([]);

// 计算属性
const canProcess = computed(() => {
  return fileList.value.length > 0 && !processing.value;
});

const resultTitle = computed(() => {
  if (!result.value) return '';
  
  if (result.value.failureCount === 0) {
    return '处理完成！所有图片配对都已成功处理';
  } else if (result.value.successCount === 0) {
    return '处理失败！没有成功处理任何图片配对';
  } else {
    return '处理完成！部分图片配对处理成功';
  }
});

const resultType = computed(() => {
  if (!result.value) return 'info';
  
  if (result.value.failureCount === 0) {
    return 'success';
  } else if (result.value.successCount === 0) {
    return 'error';
  } else {
    return 'warning';
  }
});

// 文件变更处理
const handleFileChange = (file, files) => {
  // 验证文件类型
  if (!file.name.toLowerCase().endsWith('.zip')) {
    ElMessage.error('只能上传.zip格式的压缩包文件');
    files.splice(files.indexOf(file), 1);
    return;
  }

  // 验证文件大小（100MB）
  if (file.size > 100 * 1024 * 1024) {
    ElMessage.error('压缩包文件大小不能超过100MB');
    files.splice(files.indexOf(file), 1);
    return;
  }

  fileList.value = files;
  result.value = null; // 清空之前的结果
};

// 文件移除处理
const handleFileRemove = (file, files) => {
  fileList.value = files;
  result.value = null;
};

// 清空文件
const clearFiles = () => {
  uploadRef.value.clearFiles();
  fileList.value = [];
  result.value = null;
};

// 开始处理
const startProcessing = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要处理的压缩包文件');
    return;
  }

  try {
    await ElMessageBox.confirm(
      '确定要开始处理这个压缩包吗？处理过程可能需要一些时间。',
      '确认处理',
      {
        type: 'info',
        confirmButtonText: '开始处理',
        cancelButtonText: '取消'
      }
    );

    processing.value = true;
    progressPercentage.value = 0;
    progressStatus.value = '';
    progressText.value = '正在上传文件...';
    result.value = null;

    // 模拟进度更新
    const progressInterval = setInterval(() => {
      if (progressPercentage.value < 90) {
        progressPercentage.value += Math.random() * 10;
        if (progressPercentage.value < 30) {
          progressText.value = '正在解压文件...';
        } else if (progressPercentage.value < 60) {
          progressText.value = '正在识别图片...';
        } else {
          progressText.value = '正在更新数据库...';
        }
      }
    }, 500);

    // 发送请求
    const resultData = await processLabelArchive(fileList.value[0].raw);

    clearInterval(progressInterval);
    progressPercentage.value = 100;
    progressStatus.value = 'success';
    progressText.value = '处理完成！';

    result.value = resultData;
    ElMessage.success('压缩包处理完成');

  } catch (error) {
    clearInterval(progressInterval);
    progressPercentage.value = 100;
    progressStatus.value = 'exception';
    progressText.value = '处理失败！';

    console.error('处理压缩包时发生错误:', error);
    
    if (error.name === 'cancel') {
      // 用户取消
      processing.value = false;
      progressPercentage.value = 0;
      progressText.value = '';
      return;
    }

    let errorMessage = '处理失败';
    if (error.response) {
      errorMessage = error.response.data?.message || `服务器错误 (${error.response.status})`;
    } else if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      errorMessage = '请求超时，请检查网络连接或稍后重试';
    } else {
      errorMessage = error.message || '未知错误';
    }

    ElMessage.error(errorMessage);
    
    // 设置错误结果
    result.value = {
      totalPairs: 0,
      successCount: 0,
      failureCount: 1,
      failures: [{ error: errorMessage }],
      processingTime: 0
    };

  } finally {
    processing.value = false;
  }
};

// 重置表单
const resetForm = () => {
  clearFiles();
  progressPercentage.value = 0;
  progressStatus.value = '';
  progressText.value = '';
  activeFailures.value = [];
};

// 格式化时间
const formatTime = (milliseconds) => {
  if (!milliseconds) return '0秒';
  
  const seconds = Math.floor(milliseconds / 1000);
  if (seconds < 60) {
    return `${seconds}秒`;
  }
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}分${remainingSeconds}秒`;
};
</script>

<style scoped>
.label-archive-upload {
  padding: 20px;
}

.upload-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.description {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.upload-section {
  margin-bottom: 30px;
}

.upload-dragger {
  width: 100%;
}

.action-buttons {
  margin-top: 20px;
  text-align: center;
}

.action-buttons .el-button {
  margin: 0 10px;
}

.progress-section {
  margin: 30px 0;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.progress-text {
  margin-top: 10px;
  text-align: center;
  color: #606266;
  font-size: 14px;
}

.result-section {
  margin-top: 30px;
}

.result-summary ul {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.result-summary li {
  margin: 5px 0;
}

.failure-details {
  margin-top: 20px;
}

.result-actions {
  margin-top: 20px;
  text-align: center;
}
</style>
