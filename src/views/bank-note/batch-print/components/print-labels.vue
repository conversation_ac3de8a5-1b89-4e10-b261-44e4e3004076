<template>
  <!-- 右侧滑出的打印标签抽屉 -->
  <el-drawer
    v-model="visible"
    title="打印标签预览"
    direction="rtl"
    size="60%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <!-- 工具栏 -->
    <template #header>
      <div class="drawer-header">
        <span class="drawer-title">打印标签预览</span>
        <div class="drawer-actions">
          <el-button
            type="primary"
            :icon="Printer"
            @click="handlePrint"
            :loading="printing"
            size="small"
          >
            打印
          </el-button>
          <el-button
            :icon="Download"
            @click="handleDownload"
            :loading="downloading"
            size="small"
          >
            下载
          </el-button>
        </div>
      </div>
    </template>

    <!-- 打印标签内容 -->
    <div class="print-content" v-loading="loading">
      <div class="print-info-bar">
        <el-alert
          :title="`共 ${printData?.totalCount || 0} 条记录待打印`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <div class="print-page">
        <!-- 自定义模板渲染 -->
        <div class="custom-template-container">
          <div
            v-for="(item, index) in printData?.items || []"
            :key="item.id || index"
            class="custom-label-item"
            :style="getCustomLabelStyle()"
          >
            <div
              v-for="zone in getTemplateZones()"
              :key="zone.id"
              class="template-zone"
              :data-zone-id="zone.id"
              :style="getZoneStyle(zone)"
            >
              <!-- 特殊处理评级信息区域 -->
              <div
                v-if="zone.id === 'gradeInfo' && zone.multiFieldDisplay"
                class="grade-info-container"
              >
                <div
                  v-for="(displayConfig, index) in zone.multiFieldDisplay"
                  :key="`${displayConfig.field}-${index}`"
                  class="grade-display-item"
                  :class="`grade-${displayConfig.position}`"
                  :style="getGradeDisplayStyle(displayConfig)"
                  v-html="
                    formatGradeDisplay(item[displayConfig.field], displayConfig)
                  "
                >
                </div>
              </div>

              <!-- 普通字段显示 -->
              <div v-else>
                <div
                  v-for="field in getZoneFields(zone.id, item)"
                  :key="field.name"
                  class="zone-field"
                  :style="getFieldStyle(zone, field)"
                >
                  <!-- 二维码字段特殊处理 -->
                  <div
                    v-if="isQRCodeField(field.name)"
                    v-html="formatQRCodeDisplay(field, zone)"
                  ></div>
                  <!-- 普通字段 -->
                  <div v-else v-html="formatFieldDisplay(field, zone)"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { Printer, Download } from '@element-plus/icons-vue';
  // import QRCodeComponent from '@/components/QRCodeComponent.vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    printData: {
      type: Object,
      default: null
    }
  });

  const emit = defineEmits(['update:modelValue', 'confirm-print']);

  // 响应式数据
  const loading = ref(false);
  const printing = ref(false);
  const downloading = ref(false);

  // 计算属性
  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  });

  // 处理打印
  const handlePrint = async () => {
    printing.value = true;
    try {
      // 创建新窗口进行打印，只打印标签内容
      const printWindow = window.open('', '_blank');
      const printContent = generatePrintHTML();

      printWindow.document.write(printContent);
      printWindow.document.close();

      // 等待内容加载完成后打印
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };

      emit('confirm-print', {
        ...props.printData,
        action: 'print'
      });
    } catch (error) {
      EleMessage.error('打印失败：' + error.message);
    } finally {
      printing.value = false;
    }
  };

  // 生成打印HTML内容
  const generatePrintHTML = () => {
    const labelItems = props.printData?.items || [];
    const zones = getTemplateZones();

    let labelsHTML = '';

    labelItems.forEach((item, index) => {
      labelsHTML += `
        <div class="print-label-item" style="${getPrintLabelStyle()}">
          ${zones
            .map(
              (zone) => `
            <div class="print-zone" style="${getPrintZoneStyle(zone)}">
              ${getZoneFields(zone.id, item)
                .map((field) => {
                  if (isQRCodeField(field.name)) {
                    return formatQRCodeDisplay(field, zone);
                  } else {
                    return `<div style="${getPrintFieldStyle(zone, field)}">${formatFieldDisplay(field, zone)}</div>`;
                  }
                })
                .join('')}
            </div>
          `
            )
            .join('')}
        </div>
      `;
    });

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>打印标签</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: white;
            padding: 10mm;
          }
          .print-label-item {
            page-break-inside: avoid;
            margin-bottom: 3mm;
          }
          .print-zone {
            position: absolute;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            overflow: hidden;
          }
          .qr-code-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
          }
          @media print {
            body { padding: 0; }
            .print-label-item { margin-bottom: 2mm; }
          }
        </style>
      </head>
      <body>
        ${labelsHTML}
      </body>
      </html>
    `;
  };

  // 处理下载
  const handleDownload = async () => {
    downloading.value = true;
    try {
      emit('confirm-print', {
        ...props.printData,
        action: 'download'
      });
    } catch (error) {
      EleMessage.error('下载失败：' + error.message);
    } finally {
      downloading.value = false;
    }
  };

  // 获取模板区域配置
  const getTemplateZones = () => {
    if (!props.printData?.layoutConfig?.zones) {
      console.warn('模板区域配置缺失，使用默认配置');
      return getDefaultZones();
    }
    return props.printData.layoutConfig.zones;
  };

  // 检查是否为系统模板
  const isSystemTemplate = () => {
    return (
      !props.printData?.templateId ||
      props.printData?.templateName === '系统默认模板'
    );
  };

  // 默认区域配置（当模板配置缺失时使用）
  const getDefaultZones = () => {
    return [
      {
        id: 'logo',
        name: '公司Logo',
        x: 0,
        y: 0,
        width: 30,
        height: 25,
        fontSize: 12,
        color: '#333333',
        backgroundColor: '#f5f5f5',
        textAlign: 'center'
      },
      {
        id: 'coinInfo',
        name: '钱币信息',
        x: 30,
        y: 0,
        width: 120,
        height: 25,
        fontSize: 12,
        color: '#333333',
        backgroundColor: '#ffffff',
        textAlign: 'left'
      },
      {
        id: 'gradeInfo',
        name: '评级信息',
        x: 150,
        y: 0,
        width: 30,
        height: 25,
        fontSize: 12,
        color: '#333333',
        backgroundColor: '#fff3cd',
        textAlign: 'center'
      },
      {
        id: 'qrCode',
        name: '二维码',
        x: 180,
        y: 0,
        width: 20,
        height: 25,
        fontSize: 8,
        color: '#333333',
        backgroundColor: '#ffffff',
        textAlign: 'center'
      }
    ];
  };

  // 获取自定义标签容器样式
  const getCustomLabelStyle = () => {
    const canvas = props.printData?.layoutConfig?.canvas;
    return {
      position: 'relative',
      width: canvas?.width ? `${canvas.width}mm` : '200mm',
      height: canvas?.height ? `${canvas.height}mm` : '25mm',
      border: '2px solid #2c3e50',
      borderRadius: '4px',
      marginBottom: '12px',
      backgroundColor: '#ffffff',
      pageBreakInside: 'avoid',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
      overflow: 'hidden',
      // 添加专业标签的视觉效果
      background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
      // 确保内容不会溢出
      boxSizing: 'border-box'
    };
  };

  // 获取打印标签样式（用于打印窗口）
  const getPrintLabelStyle = () => {
    const canvas = props.printData?.layoutConfig?.canvas;
    const width = canvas?.width ? `${canvas.width}mm` : '200mm';
    const height = canvas?.height ? `${canvas.height}mm` : '25mm';

    return `
      position: relative;
      width: ${width};
      height: ${height};
      border: 2px solid #2c3e50;
      border-radius: 2px;
      background: #ffffff;
      overflow: hidden;
      box-sizing: border-box;
    `;
  };

  // 获取打印区域样式
  const getPrintZoneStyle = (zone) => {
    return `
      position: absolute;
      left: ${zone.x || 0}mm;
      top: ${zone.y || 0}mm;
      width: ${zone.width || 20}mm;
      height: ${zone.height || 10}mm;
      font-size: ${zone.fontSize || 12}px;
      color: ${zone.color || '#2c3e50'};
      font-weight: ${zone.fontWeight || 'normal'};
      text-align: ${zone.textAlign || 'left'};
      padding: ${zone.padding ? `${zone.padding}px` : '4px'};
      line-height: ${zone.lineHeight || 1.3};
      overflow: hidden;
    `;
  };

  // 获取打印字段样式
  const getPrintFieldStyle = (zone, field) => {
    const fieldStyle = zone?.fieldStyles?.[field.name] || {};
    const getFieldDefaults = (fieldName) => {
      if (fieldName === 'gradeScoreNumber') {
        return { fontSize: '32px', fontWeight: 'bold', color: '#e74c3c' };
      } else if (fieldName === 'gradeScoreText') {
        return { fontSize: '10px', fontWeight: 'normal', color: '#7f8c8d' };
      } else if (fieldName === 'bankName') {
        return { fontSize: '14px', fontWeight: 'bold', color: '#2c3e50' };
      } else if (fieldName === 'coinName') {
        return { fontSize: '12px', fontWeight: '600', color: '#34495e' };
      }
      return { fontSize: '12px', fontWeight: 'normal', color: '#2c3e50' };
    };

    const defaults = getFieldDefaults(field.name);

    return `
      font-size: ${fieldStyle.fontSize ? `${fieldStyle.fontSize}px` : defaults.fontSize};
      font-weight: ${fieldStyle.fontWeight || defaults.fontWeight};
      color: ${fieldStyle.color || defaults.color};
      margin-bottom: 2px;
      line-height: 1.3;
    `;
  };

  // 获取区域样式
  const getZoneStyle = (zone) => {
    // 根据区域类型设置不同的默认样式
    const getZoneDefaults = (zoneId) => {
      switch (zoneId) {
        case 'logo':
          return {
            backgroundColor: '#f8f9fa',
            border: '1px solid #e9ecef',
            textAlign: 'center',
            fontWeight: 'bold'
          };
        case 'gradeInfo':
          return {
            backgroundColor: '#fff3cd',
            border: '1px solid #ffeaa7',
            textAlign: 'center',
            fontWeight: 'bold'
          };
        case 'qrCode':
          return {
            backgroundColor: '#ffffff',
            border: '1px solid #dee2e6',
            textAlign: 'center'
          };
        default:
          return {
            backgroundColor: 'transparent',
            textAlign: 'left'
          };
      }
    };

    const defaults = getZoneDefaults(zone.id);

    return {
      position: 'absolute',
      left: `${zone.x || 0}mm`,
      top: `${zone.y || 0}mm`,
      width: `${zone.width || 20}mm`,
      height: `${zone.height || 10}mm`,
      fontSize: `${zone.fontSize || 12}px`,
      color: zone.color || '#2c3e50',
      backgroundColor: zone.backgroundColor || defaults.backgroundColor,
      fontWeight: zone.fontWeight || defaults.fontWeight || 'normal',
      textAlign: zone.textAlign || defaults.textAlign || 'left',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: zone.verticalAlign || 'center',
      alignItems: zone.textAlign === 'center' ? 'center' : 'flex-start',
      padding: zone.padding ? `${zone.padding}px` : '4px',
      border: zone.showBorder
        ? `1px solid ${zone.borderColor || '#ccc'}`
        : defaults.border || 'none',
      borderRadius: zone.borderRadius ? `${zone.borderRadius}px` : '2px',
      overflow: 'hidden',
      lineHeight: zone.lineHeight || 1.3,
      boxSizing: 'border-box',
      // 添加文字阴影以提高可读性
      textShadow: zone.id === 'gradeInfo' ? '0 1px 2px rgba(0,0,0,0.1)' : 'none'
    };
  };

  // 获取字段样式
  const getFieldStyle = (zone, field) => {
    // 获取字段样式配置
    const fieldStyle = zone?.fieldStyles?.[field.name] || {};

    // 根据字段类型设置默认样式
    const getFieldDefaults = (fieldName, zoneId) => {
      // 评级相关字段
      if (fieldName === 'gradeScoreNumber') {
        return {
          fontSize: '32px',
          fontWeight: 'bold',
          color: '#e74c3c',
          textAlign: 'center'
        };
      } else if (fieldName === 'gradeScoreText') {
        return {
          fontSize: '10px',
          fontWeight: 'normal',
          color: '#7f8c8d',
          textAlign: 'center'
        };
      } else if (fieldName === 'gradeScore') {
        return {
          fontSize: '16px',
          fontWeight: 'bold',
          color: '#e74c3c'
        };
      }

      // 银行名称
      if (fieldName === 'bankName') {
        return {
          fontSize: '14px',
          fontWeight: 'bold',
          color: '#2c3e50'
        };
      }

      // 钱币名称
      if (fieldName === 'coinName') {
        return {
          fontSize: '12px',
          fontWeight: '600',
          color: '#34495e'
        };
      }

      // 序列号
      if (fieldName === 'serialNumber') {
        return {
          fontSize: '11px',
          fontWeight: 'normal',
          color: '#7f8c8d'
        };
      }

      // 年份信息
      if (fieldName === 'yearInfo') {
        return {
          fontSize: '11px',
          fontWeight: 'normal',
          color: '#95a5a6'
        };
      }

      // 默认样式
      return {
        fontSize: '12px',
        fontWeight: 'normal',
        color: '#2c3e50'
      };
    };

    const defaults = getFieldDefaults(field.name, zone.id);

    return {
      lineHeight: zone.lineHeight || 1.3,
      marginBottom: zone.fieldSpacing ? `${zone.fieldSpacing}px` : '2px',
      wordBreak: field.name === 'serialNumber' ? 'break-all' : 'normal',
      fontSize: fieldStyle.fontSize
        ? `${fieldStyle.fontSize}px`
        : field.fontSize
          ? `${field.fontSize}px`
          : defaults.fontSize,
      fontWeight:
        fieldStyle.fontWeight || field.fontWeight || defaults.fontWeight,
      color: fieldStyle.color || field.color || defaults.color,
      textAlign: fieldStyle.textAlign || defaults.textAlign || 'inherit',
      // 添加字体平滑
      WebkitFontSmoothing: 'antialiased',
      MozOsxFontSmoothing: 'grayscale'
    };
  };

  // 获取区域字段数据
  const getZoneFields = (zoneId, item) => {
    try {
      // 获取字段映射配置
      const fieldMapping = props.printData?.fieldMapping?.[zoneId] || [];

      console.log(`获取区域 ${zoneId} 字段数据:`, {
        fieldMapping,
        isSystemTemplate: isSystemTemplate(),
        printData: props.printData
      });

      if (!fieldMapping.length) {
        console.warn(`区域 ${zoneId} 没有配置字段映射，使用默认配置`);
        // 如果是系统模板，提供默认字段映射
        if (isSystemTemplate()) {
          const defaultFields = getDefaultZoneFields(zoneId, item);
          console.log(`使用默认字段映射:`, defaultFields);
          return defaultFields;
        }
        return [];
      }

      // 获取区域配置信息
      const zone = props.printData?.layoutConfig?.zones?.find(
        (z) => z.id === zoneId
      );

      // 从钱币数据中提取字段值
      return fieldMapping.map((fieldName) => {
        let fieldValue = '';

        // 优先从自定义字段中获取
        if (item.customFields?.[zoneId]?.[fieldName]) {
          fieldValue = item.customFields[zoneId][fieldName];
        } else {
          // 从钱币基础数据中获取
          fieldValue = getFieldValueFromItem(item, fieldName, zone);
        }

        return {
          name: fieldName,
          value: fieldValue || '',
          displayName: getFieldDisplayName(fieldName, zone)
        };
      });
    } catch (error) {
      console.error(`获取区域 ${zoneId} 字段数据失败:`, error);
      return [];
    }
  };

  // 获取默认区域字段数据（用于系统模板）
  const getDefaultZoneFields = (zoneId, item) => {
    const defaultFields = {
      logo: [
        { name: 'companyLogo', value: 'CMG', displayName: '公司Logo' },
        { name: 'companyName', value: '中乾评级', displayName: '公司名称' }
      ],
      coinInfo: [
        {
          name: 'bankName',
          value: item.bankName || '',
          displayName: '发行银行'
        },
        {
          name: 'yearInfo',
          value: item.yearInfo || '',
          displayName: '年代信息'
        },
        {
          name: 'coinName',
          value: item.coinName || '',
          displayName: '钱币名称'
        },
        {
          name: 'serialNumber',
          value: item.serialNumber || '',
          displayName: '序列号'
        },
        { name: 'version', value: item.version || '', displayName: '版别' },
        {
          name: 'customerName',
          value: item.customerName || '',
          displayName: '客户姓名'
        }
      ],
      gradeInfo: [
        {
          name: 'gradeScore',
          value: item.gradeScore || '',
          displayName: '评级分数'
        },
        {
          name: 'gradeLevel',
          value: item.gradeLevel || '',
          displayName: '评级等级'
        },
        {
          name: 'specialMark',
          value: item.specialMark || '',
          displayName: '特殊标记'
        },
        {
          name: 'authenticity',
          value: item.authenticity || '',
          displayName: '真伪性'
        },
        { name: 'diyCode', value: item.diyCode || '', displayName: '送评条码' }
      ],
      qrCode: [
        {
          name: 'qrCode',
          value: item.diyCode || item.serialNumber || '',
          displayName: '二维码'
        },
        {
          name: 'qrCodeContent',
          value: item.diyCode || item.serialNumber || '',
          displayName: '二维码内容'
        }
      ]
    };

    return defaultFields[zoneId] || [];
  };

  // 从钱币数据中获取字段值
  const getFieldValueFromItem = (item, fieldName, zone) => {
    // 检查是否是组合字段
    if (zone && zone.combinationFields && zone.combinationFields[fieldName]) {
      return formatCombinationField(item, zone.combinationFields[fieldName]);
    }

    const fieldMap = {
      // 基础信息
      bankName: item.bankName,
      coinName: item.coinName,
      coinName1: item.coinName,
      yearInfo: item.yearInfo,
      serialNumber: item.serialNumber,
      version: item.version,
      additionalInfo: item.additionalInfo,

      // 特殊组合字段：编号-版别（保持向下兼容）
      serialNumberWithVersion: formatSerialNumberWithVersion(item),

      // 评级信息
      gradeScore: item.gradeScore,
      gradeScoreValue: item.gradeScoreValue, // 直接使用后端提供的数字值
      gradeScoreNumber:
        item.gradeScoreValue || extractGradeScoreNumber(item.gradeScore), // 优先使用gradeScoreValue，兼容旧数据
      gradeScoreText: extractGradeScoreText(item.gradeScore, item), // 仅文字部分，传入完整item以便获取其他字段
      gradeLevel: item.gradeLevel,
      specialMark: item.specialMark,
      authenticity: item.authenticity,

      // 客户信息
      customerName: item.customerName,
      diyCode: item.diyCode,

      // 物理属性
      weight: item.weight,
      size: item.size,
      coinType: item.coinType,

      // 其他
      diyCode: item.diyCode,
      printStatus: item.printStatus
    };

    return fieldMap[fieldName] || '';
  };

  // 格式化组合字段
  const formatCombinationField = (item, combinationConfig) => {
    // 直接获取基础字段值，避免递归调用
    const firstValue =
      getBasicFieldValue(item, combinationConfig.firstField) || '';
    const secondValue =
      getBasicFieldValue(item, combinationConfig.secondField) || '';

    if (combinationConfig.layout === 'vertical') {
      // 分行显示
      const parts = [];
      if (firstValue) parts.push(firstValue);
      if (secondValue) parts.push(secondValue);
      return parts.join('\n');
    } else {
      // 同一行显示
      if (firstValue && secondValue) {
        return `${firstValue}${combinationConfig.separator}${secondValue}`;
      } else if (firstValue) {
        return firstValue;
      } else if (secondValue) {
        return secondValue;
      }
      return '';
    }
  };

  // 获取基础字段值（不处理组合字段）
  const getBasicFieldValue = (item, fieldName) => {
    const fieldMap = {
      // 基础信息
      bankName: item.bankName,
      coinName: item.coinName,
      coinName1: item.coinName,
      yearInfo: item.yearInfo,
      serialNumber: item.serialNumber,
      version: item.version,
      additionalInfo: item.additionalInfo,

      // 评级信息
      gradeScore: item.gradeScore,
      gradeScoreValue: item.gradeScoreValue, // 直接使用后端提供的数字值
      gradeScoreNumber:
        item.gradeScoreValue || extractGradeScoreNumber(item.gradeScore), // 优先使用gradeScoreValue，兼容旧数据
      gradeScoreText: extractGradeScoreText(item.gradeScore, item), // 仅文字部分，传入完整item以便获取其他字段
      gradeLevel: item.gradeLevel,
      specialMark: item.specialMark,
      authenticity: item.authenticity,

      // 客户信息
      customerName: item.customerName,
      diyCode: item.diyCode,

      // 物理属性
      weight: item.weight,
      size: item.size,
      coinType: item.coinType,

      // 其他
      diyCode: item.diyCode,
      printStatus: item.printStatus,

      // 二维码相关
      qrCode: item.diyCode, // 二维码内容使用送评条码
      qrCodeContent: item.diyCode
    };

    return fieldMap[fieldName] || '';
  };

  // 格式化编号-版别（保持向下兼容）
  const formatSerialNumberWithVersion = (item) => {
    const serialNumber = item.serialNumber || '';
    const version = item.version || '';

    if (serialNumber && version) {
      return `${serialNumber} - ${version}`;
    } else if (serialNumber) {
      return serialNumber;
    } else if (version) {
      return version;
    }
    return '';
  };

  // 获取字段显示名称
  const getFieldDisplayName = (fieldName, zone) => {
    // 检查是否是组合字段
    if (zone && zone.combinationFields && zone.combinationFields[fieldName]) {
      return zone.combinationFields[fieldName].displayName || fieldName;
    }

    const displayNames = {
      bankName: '发行银行',
      coinName: '钱币名称',
      coinName1: '钱币名称',
      yearInfo: '年代信息',
      serialNumber: '序列号',
      version: '版别',
      serialNumberWithVersion: '编号-版别',
      additionalInfo: '附加信息',
      gradeScore: '评级分数',
      gradeScoreNumber: '评级分数（仅数字）',
      gradeScoreText: '评级等级（仅文字）',
      gradeLevel: '评级等级',
      specialMark: '特殊标记',
      authenticity: '真伪性',
      customerName: '客户姓名',
      diyCode: '送评条码',
      weight: '重量',
      size: '尺寸',
      coinType: '钱币类型',
      diyCode: '送评条码',
      printStatus: '打印状态',
      qrCode: '二维码',
      qrCodeContent: '二维码内容'
    };

    return displayNames[fieldName] || fieldName;
  };

  // 解析评级信息
  const parseGradeInfo = (gradeScore) => {
    if (!gradeScore) return { score: '', level: '', mark: '' };

    const value = gradeScore.toString().trim();
    console.log('解析评级信息:', value);

    // 提取数字分数（支持整数和小数，可能在末尾或开头）
    let score = '';
    let levelPart = '';

    // 情况1: 数字在末尾 (如 "Superb Gem Unc68")
    const endScoreMatch = value.match(/^(.+?)(\d+(?:\.\d+)?)$/);
    if (endScoreMatch) {
      levelPart = endScoreMatch[1].trim();
      score = endScoreMatch[2];
      console.log('数字在末尾:', { score, levelPart });
    } else {
      // 情况2: 数字在开头 (如 "68 Superb Gem Unc")
      const startScoreMatch = value.match(/^(\d+(?:\.\d+)?)\s*(.+)$/);
      if (startScoreMatch) {
        score = startScoreMatch[1];
        levelPart = startScoreMatch[2].trim();
        console.log('数字在开头:', { score, levelPart });
      } else {
        // 情况3: 只有数字 (如 "68")
        const onlyNumberMatch = value.match(/^\d+(?:\.\d+)?$/);
        if (onlyNumberMatch) {
          score = value;
          levelPart = '';
          console.log('只有数字:', { score, levelPart });
        } else {
          // 情况4: 只有文字 (如 "Superb Gem Unc")
          score = '';
          levelPart = value;
          console.log('只有文字:', { score, levelPart });
        }
      }
    }

    // 解析等级部分，保持完整的等级描述
    const level = levelPart || '';

    console.log('最终解析结果:', { score, level });
    return { score, level, mark: '' };
  };

  // 格式化字段显示
  const formatFieldDisplay = (field, zone) => {
    const fieldStyle = zone?.fieldStyles?.[field.name];
    const value = field.value || '';

    // 特殊处理二维码字段
    if (field.name === 'qrCode' || field.name === 'qrCodeContent') {
      return formatQRCode(value, fieldStyle);
    }

    if (!fieldStyle || fieldStyle.displayMode === 'normal') {
      return value;
    }

    switch (fieldStyle.displayMode) {
      case 'large':
        return `<span style="font-size: ${fieldStyle.fontSize || 18}px; font-weight: bold;">${value}</span>`;

      case 'vertical':
        // 竖向显示字符，每个字符一行
        const chars = value.split('');
        const letterSpacing = fieldStyle.letterSpacing || 2;
        return chars
          .map(
            (char) =>
              `<div style="line-height: 1; margin-bottom: ${letterSpacing}px;">${char}</div>`
          )
          .join('');

      case 'grade-layered':
        // 评级信息分层显示：分数大字体在上，等级小字体在下
        const gradeInfo = parseGradeInfo(value);
        const baseFontSize = fieldStyle.fontSize || 12;
        const lineHeight = fieldStyle.lineHeight || 1.2;

        // 配置字体大小：数字部分更大，文字部分更小
        const scoreFontSize =
          fieldStyle.scoreFontSize || Math.max(36, baseFontSize * 3); // 增大数字字体
        const levelFontSize =
          fieldStyle.levelFontSize || Math.max(9, baseFontSize * 0.75); // 文字字体

        console.log('评级分层显示调试:', {
          fieldStyle,
          gradeInfo,
          scoreFontSize,
          levelFontSize,
          configuredScoreFontSize: fieldStyle.scoreFontSize,
          configuredLevelFontSize: fieldStyle.levelFontSize
        });

        if (gradeInfo.score && gradeInfo.level) {
          // 有数字和文字，分层显示
          return `
            <div style="
              text-align: center;
              line-height: ${lineHeight};
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              height: 100%;
            ">
              <div style="
                font-size: ${scoreFontSize}px;
                font-weight: bold;
                color: #e74c3c;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
                margin-bottom: 1px;
                line-height: 0.9;
              ">${gradeInfo.score}</div>
              <div style="
                font-size: ${levelFontSize}px;
                color: #7f8c8d;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                line-height: 1.1;
              ">${gradeInfo.level}</div>
            </div>
          `;
        } else if (gradeInfo.score) {
          // 只有数字
          return `<div style="
            text-align: center;
            font-size: ${scoreFontSize}px;
            font-weight: bold;
            color: #e74c3c;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
          ">${gradeInfo.score}</div>`;
        } else if (gradeInfo.level) {
          // 只有文字
          return `<div style="
            text-align: center;
            font-size: ${levelFontSize}px;
            color: #7f8c8d;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
          ">${gradeInfo.level}</div>`;
        } else {
          // 显示完整内容
          return `<div style="
            text-align: center;
            font-size: ${baseFontSize}px;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
          ">${value}</div>`;
        }

      case 'grade-score-only':
        // 只显示评级分数
        const scoreInfo = parseGradeInfo(value);
        // const scoreFontSize = fieldStyle.fontSize || 24;
        return scoreInfo.score
          ? `<span style="font-size: ${scoreFontSize}px; font-weight: bold;">${scoreInfo.score}</span>`
          : `<span style="font-size: ${scoreFontSize}px; font-weight: bold;">${value}</span>`;

      case 'grade-level-only':
        // 只显示评级等级
        const levelInfo = parseGradeInfo(value);
        // const levelFontSize = fieldStyle.fontSize || 10;
        return levelInfo.level
          ? `<span style="font-size: ${levelFontSize}px;">${levelInfo.level}</span>`
          : `<span style="font-size: ${levelFontSize}px;">${value}</span>`;

      case 'grade-mark-only':
        // 只显示特殊标记（竖向）
        const markInfo = parseGradeInfo(value);
        const markFontSize = fieldStyle.fontSize || 10;
        const markSpacing = fieldStyle.letterSpacing || 2;

        if (markInfo.mark) {
          const chars = markInfo.mark.split('');
          return chars
            .map(
              (char) =>
                `<div style="line-height: 1; margin-bottom: ${markSpacing}px; font-size: ${markFontSize}px;">${char}</div>`
            )
            .join('');
        } else {
          return `<span style="font-size: ${markFontSize}px;">${value}</span>`;
        }

      case 'layered':
        // 通用分层显示
        const parts = value.split(' ');
        const generalLineHeight = fieldStyle.lineHeight || 1.2;
        const generalFontSize = fieldStyle.fontSize || 12;

        if (parts.length >= 2) {
          const firstPart = parts[0];
          const restParts = parts.slice(1).join(' ');

          return `
            <div style="text-align: center; line-height: ${generalLineHeight};">
              <div style="font-size: ${generalFontSize + 6}px; font-weight: bold; margin-bottom: 2px;">${firstPart}</div>
              <div style="font-size: ${generalFontSize - 2}px;">${restParts}</div>
            </div>
          `;
        } else {
          return `<span style="font-size: ${generalFontSize}px;">${value}</span>`;
        }

      default:
        return value;
    }
  };

  // 格式化评级信息显示（用于多字段显示配置）
  const formatGradeDisplay = (value, displayConfig) => {
    const gradeInfo = parseGradeInfo(value);
    const fontSize = displayConfig.fontSize || 12;
    const letterSpacing = displayConfig.letterSpacing || 2;

    switch (displayConfig.displayMode) {
      case 'grade-score-only':
        return gradeInfo.score
          ? `<span style="font-size: ${fontSize}px; font-weight: bold;">${gradeInfo.score}</span>`
          : '';

      case 'grade-level-only':
        return gradeInfo.level
          ? `<span style="font-size: ${fontSize}px;">${gradeInfo.level}</span>`
          : '';

      case 'grade-mark-only':
        if (gradeInfo.mark) {
          const chars = gradeInfo.mark.split('');
          return chars
            .map(
              (char) =>
                `<div style="line-height: 1; margin-bottom: ${letterSpacing}px; font-size: ${fontSize}px;">${char}</div>`
            )
            .join('');
        }
        return '';

      default:
        return value || '';
    }
  };

  // 获取评级显示样式
  const getGradeDisplayStyle = (displayConfig) => {
    const baseStyle = {
      position: 'absolute',
      fontSize: `${displayConfig.fontSize || 12}px`
    };

    // 根据位置设置样式
    switch (displayConfig.position) {
      case 'top':
        return {
          ...baseStyle,
          top: '2px',
          left: '50%',
          transform: 'translateX(-50%)',
          textAlign: 'center'
        };

      case 'middle':
        return {
          ...baseStyle,
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center'
        };

      case 'bottom':
        return {
          ...baseStyle,
          bottom: '2px',
          left: '50%',
          transform: 'translateX(-50%)',
          textAlign: 'center'
        };

      case 'right':
        return {
          ...baseStyle,
          top: '2px',
          right: '2px',
          textAlign: 'center'
        };

      case 'left':
        return {
          ...baseStyle,
          top: '2px',
          left: '2px',
          textAlign: 'center'
        };

      default:
        return baseStyle;
    }
  };

  // 格式化二维码显示
  const formatQRCode = (value, fieldStyle) => {
    if (!value) return '';

    const size = fieldStyle?.qrSize || 50; // 调整默认二维码大小
    const showText = fieldStyle?.showText !== false; // 默认显示文本
    const textSize = fieldStyle?.textSize || 8; // 调整文本大小

    // 生成二维码HTML
    let qrHtml = `<div class="qr-code-container" style="
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
    ">`;

    // 二维码图片占位符（实际应用中需要集成二维码生成库）
    qrHtml += `<div class="qr-code-placeholder" style="
      width: ${size}px;
      height: ${size}px;
      border: 1px solid #2c3e50;
      margin: 0 auto 2px auto;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 6px;
      background: repeating-linear-gradient(
        45deg,
        #000,
        #000 1px,
        #fff 1px,
        #fff 3px
      );
      border-radius: 2px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    ">`;

    // 如果无法生成二维码，显示占位符
    qrHtml += `<span style="
      font-size: 6px;
      color: #fff;
      background: rgba(0,0,0,0.7);
      padding: 1px 2px;
      border-radius: 1px;
    ">QR</span>`;
    qrHtml += `</div>`;

    // 显示二维码内容文本
    if (showText) {
      qrHtml += `<div class="qr-code-text" style="
        font-size: ${textSize}px;
        line-height: 1.1;
        word-break: break-all;
        margin-top: 1px;
        color: #7f8c8d;
        font-weight: 500;
        max-width: ${size + 10}px;
        text-align: center;
      ">${value}</div>`;
    }

    qrHtml += `</div>`;

    return qrHtml;
  };

  // 生成简单的二维码SVG（占位符实现）
  const generateQRCodeSVG = (text) => {
    // 这里是一个简化的二维码SVG生成，实际应用中应该使用专业的二维码库
    const svg = `
      <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
        <rect width="100" height="100" fill="white"/>
        <rect x="10" y="10" width="20" height="20" fill="black"/>
        <rect x="70" y="10" width="20" height="20" fill="black"/>
        <rect x="10" y="70" width="20" height="20" fill="black"/>
        <rect x="40" y="40" width="20" height="20" fill="black"/>
        <!-- 更多二维码模式点... -->
        <text x="50" y="95" text-anchor="middle" font-size="8" fill="black">${text.substring(0, 10)}</text>
      </svg>
    `;
    return btoa(svg);
  };

  // 判断是否为二维码字段
  const isQRCodeField = (fieldName) => {
    return fieldName === 'qrCode' || fieldName === 'qrCodeContent';
  };

  // 获取二维码大小
  const getQRCodeSize = (zone, fieldName) => {
    const fieldStyle = zone?.fieldStyles?.[fieldName];
    return fieldStyle?.qrSize || 60;
  };

  // 获取二维码是否显示文本
  const getQRCodeShowText = (zone, fieldName) => {
    const fieldStyle = zone?.fieldStyles?.[fieldName];
    return fieldStyle?.showText !== false;
  };

  // 获取二维码文本大小
  const getQRCodeTextSize = (zone, fieldName) => {
    const fieldStyle = zone?.fieldStyles?.[fieldName];
    return fieldStyle?.textSize || 10;
  };

  // 提取评级分数的数字部分
  const extractGradeScoreNumber = (gradeScore) => {
    if (!gradeScore) return '';

    console.log('原始评级分数数据:', gradeScore, '类型:', typeof gradeScore);

    // 提取数字部分，支持整数和小数
    const numberMatch = gradeScore.toString().match(/\d+(\.\d+)?/);
    const result = numberMatch ? numberMatch[0] : '';

    console.log('提取的数字部分:', result);
    return result;
  };

  // 提取评级分数的文字部分
  const extractGradeScoreText = (gradeScore, item = null) => {
    console.log('=== 提取评级文字部分 ===');
    console.log('gradeScore:', gradeScore);
    console.log('item对象:', item);

    if (!gradeScore && !item) return '';

    let textPart = '';

    // 如果有gradeScore，先尝试从中提取文字
    if (gradeScore) {
      const gradeStr = gradeScore.toString().trim();
      console.log('gradeScore字符串:', gradeStr);

      // 方式1: 如果数字在末尾 (如 "Superb Gem Unc68")
      const endNumberMatch = gradeStr.match(/^(.+?)(\d+(\.\d+)?)$/);
      if (endNumberMatch && endNumberMatch[1].trim()) {
        textPart = endNumberMatch[1].trim();
        console.log('数字在末尾格式，提取文字:', textPart);
      }

      // 方式2: 如果数字在开头 (如 "68 Superb Gem Unc")
      if (!textPart) {
        const startNumberMatch = gradeStr.match(/^(\d+(\.\d+)?)\s*(.+)$/);
        if (startNumberMatch && startNumberMatch[3].trim()) {
          textPart = startNumberMatch[3].trim();
          console.log('数字在开头格式，提取文字:', textPart);
        }
      }

      // 方式3: 简单移除所有数字 (如 "MS68" -> "MS")
      if (!textPart) {
        const cleanText = gradeStr.replace(/\d+(\.\d+)?/g, '').trim();
        if (cleanText && cleanText.length > 0) {
          textPart = cleanText;
          console.log('简单移除数字，提取文字:', textPart);
        }
      }

      // 方式4: 如果还是为空，可能整个字段就是文字
      if (!textPart && !/\d/.test(gradeStr)) {
        textPart = gradeStr;
        console.log('整个字段为文字:', textPart);
      }
    }

    // 如果从gradeScore中没有提取到文字，尝试从其他字段获取
    if (!textPart && item) {
      console.log('从gradeScore中未提取到文字，尝试其他字段...');

      // 优先级：specialMark > rank > gradeLevel
      if (item.specialMark && item.specialMark.trim()) {
        textPart = item.specialMark.trim();
        console.log('从specialMark获取:', textPart);
      } else if (item.rank && item.rank.trim()) {
        textPart = item.rank.trim();
        console.log('从rank获取:', textPart);
      } else if (item.gradeLevel && item.gradeLevel.trim()) {
        textPart = item.gradeLevel.trim();
        console.log('从gradeLevel获取:', textPart);
      }
    }

    console.log('最终提取的文字部分:', textPart);
    return textPart || '';
  };

  // 格式化二维码显示（简化版本）
  const formatQRCodeDisplay = (field, zone) => {
    const value = field.value || '';
    if (!value) {
      console.warn('二维码字段值为空:', field);
      return '<div style="text-align: center; color: #999; font-size: 10px;">无二维码</div>';
    }

    const size = getQRCodeSize(zone, field.name);
    const showText = getQRCodeShowText(zone, field.name);
    const textSize = getQRCodeTextSize(zone, field.name);

    console.log('生成二维码显示:', { value, size, showText, textSize });

    // 生成简化的二维码HTML
    let qrHtml = `<div class="qr-code-container" style="
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 2px;
    ">`;

    // 使用后端二维码生成服务（统一质量，避免 BufferedImageLuminanceSource 识别问题）
    // const qrCodeUrl = `/api/batch-print/generatingQRCode?codeText=${encodeURIComponent(value)}`;
    const qrCodeUrl = ``;

    qrHtml += `<img src="${qrCodeUrl}" alt="QR Code" style="
      width: ${size}px;
      height: ${size}px;
      border: 1px solid #2c3e50;
      border-radius: 2px;
      display: block;
      margin: 0 auto 2px auto;
      box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    " onerror="console.log('二维码加载失败，显示占位符'); this.style.display='none'; this.nextElementSibling.style.display='flex';" />`;

    // 备用占位符 - 更专业的样式
    qrHtml += `<div style="
      width: ${size}px;
      height: ${size}px;
      border: 1px solid #2c3e50;
      border-radius: 2px;
      margin: 0 auto 2px auto;
      display: none;
      align-items: center;
      justify-content: center;
      font-size: 6px;
      background: repeating-linear-gradient(
        45deg,
        #000,
        #000 1px,
        #fff 1px,
        #fff 3px
      );
      box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    ">
      <span style="
        color: #fff;
        background: rgba(0,0,0,0.7);
        padding: 1px 2px;
        border-radius: 1px;
        font-size: 6px;
      ">QR</span>
    </div>`;

    // 显示二维码内容文本
    if (showText) {
      qrHtml += `<div class="qr-code-text" style="
        font-size: ${textSize}px;
        line-height: 1.1;
        word-break: break-all;
        margin-top: 1px;
        text-align: center;
        color: #7f8c8d;
        font-weight: 500;
        max-width: ${size + 10}px;
      ">${value}</div>`;
    }

    qrHtml += `</div>`;

    return qrHtml;
  };

  // 暴露方法供外部调用
  defineExpose({
    // 可以在这里添加需要暴露给父组件的方法
  });
</script>

<style scoped>
  /* 抽屉头部样式 */
  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .drawer-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .drawer-actions {
    display: flex;
    gap: 8px;
  }

  /* 打印内容区域 */
  .print-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #f5f7fa;
  }

  .print-info-bar {
    margin-bottom: 16px;
    flex-shrink: 0;
  }

  .print-page {
    flex: 1;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    overflow-y: auto;
  }

  /* 自定义模板容器样式 */
  .custom-template-container {
    width: 100%;
  }

  .custom-label-item {
    margin-bottom: 12px;
    transition: all 0.3s ease;
    transform: translateZ(0); /* 启用硬件加速 */
  }

  .custom-label-item:hover {
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
    transform: translateY(-1px);
  }

  .template-zone {
    box-sizing: border-box;
    /* 添加平滑的过渡效果 */
    transition: all 0.2s ease;
  }

  .template-zone:hover {
    /* 预览时的轻微高亮效果 */
    background-color: rgba(64, 158, 255, 0.05) !important;
  }

  .zone-field {
    font-size: inherit;
    line-height: 1.3;
    /* 改进文字渲染 */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .grade-info-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .grade-display-item {
    position: absolute;
  }

  .grade-top {
    top: 2px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
  }

  .grade-middle {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
  }

  .grade-bottom {
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
  }

  .grade-right {
    top: 2px;
    right: 2px;
    text-align: center;
  }

  .grade-left {
    top: 2px;
    left: 2px;
    text-align: center;
  }

  /* 预定义模板样式（保持原有样式） */
  .predefined-template-container {
    width: 100%;
  }

  .label-item {
    width: 100%;
    height: 80px;
    border: 1px solid #dcdfe6;
    border-top: none;
    display: flex;
    align-items: center;
    page-break-inside: avoid;
    background: white;
    transition: all 0.2s;
  }

  .label-item:hover {
    background: #f8f9fa;
    border-color: #409eff;
  }

  .label-item:first-child {
    border-top: 1px solid #dcdfe6;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  .label-item:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  .label-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 8px 16px;
  }

  /* Logo区域样式 */
  .logo-section {
    width: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .cmg-logo {
    text-align: center;
  }

  .logo-symbol {
    font-size: 18px;
    font-weight: bold;
    color: #d4af37;
    line-height: 1;
    margin-bottom: 2px;
  }

  .logo-text {
    font-size: 8px;
    color: #606266;
    line-height: 1;
  }

  /* 钱币信息样式 */
  .coin-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: left;
    padding-left: 16px;
  }

  .bank-name {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 4px;
    color: #303133;
  }

  .coin-year {
    font-size: 12px;
    margin-bottom: 2px;
    color: #606266;
  }

  .coin-serial {
    font-size: 10px;
    color: #909399;
  }

  /* 评级和二维码区域样式 */
  .grade-qr-section {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .grade-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
  }

  .grade-score {
    font-size: 36px;
    font-weight: bold;
    color: #e6a23c;
    line-height: 1;
    margin-bottom: 2px;
  }

  .grade-level-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
  }

  .grade-level {
    font-size: 8px;
    color: #606266;
    text-align: center;
    margin-bottom: 2px;
    max-width: 80px;
    word-wrap: break-word;
  }

  .special-mark {
    font-size: 6px;
    color: #909399;
    writing-mode: vertical-rl;
    text-orientation: upright;
    line-height: 1;
    margin-left: 2px;
  }

  /* 二维码样式 */
  .qr-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
  }

  .qr-code {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .qr-placeholder {
    width: 60px;
    height: 60px;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
    background: white;
  }

  .qr-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(8, 1fr);
    gap: 1px;
    width: 50px;
    height: 50px;
  }

  .qr-dot {
    background: white;
  }

  .qr-dot.active {
    background: black;
  }

  .qr-number {
    font-size: 8px;
    color: #909399;
    text-align: center;
    max-width: 80px;
    word-break: break-all;
  }

  /* 新增：专业标签样式增强 */
  .custom-label-item .template-zone[data-zone-id='logo'] {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }

  .custom-label-item .template-zone[data-zone-id='gradeInfo'] {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  }

  .custom-label-item .template-zone[data-zone-id='qrCode'] {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  }

  /* 评级信息特殊样式 */
  .grade-info-container .grade-display-item {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* 二维码容器样式 */
  .qr-code-container {
    filter: contrast(1.1) brightness(0.95);
  }

  /* 字段文本优化 */
  .zone-field {
    /* 防止文本选择影响预览效果 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* 打印样式 */
  @media print {
    :deep(.el-drawer) {
      position: static !important;
      transform: none !important;
      width: 100% !important;
      height: auto !important;
      box-shadow: none !important;
    }

    :deep(.el-drawer__header) {
      display: none !important;
    }

    :deep(.el-drawer__body) {
      padding: 0 !important;
    }

    .print-content {
      background: white !important;
      height: auto !important;
    }

    .print-info-bar {
      display: none !important;
    }

    .print-page {
      box-shadow: none !important;
      border-radius: 0 !important;
      padding: 1cm !important;
      width: 21cm !important;
      margin: 0 auto !important;
    }

    /* 自定义模板打印样式 */
    .custom-label-item {
      margin-bottom: 3mm !important;
      border: 2px solid #2c3e50 !important;
      border-radius: 2px !important;
      page-break-inside: avoid;
      box-shadow: none !important;
      background: #ffffff !important;
      /* 确保打印时的精确尺寸 */
      -webkit-print-color-adjust: exact;
      color-adjust: exact;
    }

    .template-zone {
      border: none !important;
      /* 打印时移除hover效果 */
      background-color: transparent !important;
    }

    .template-zone:hover {
      background-color: transparent !important;
    }

    /* 预定义模板打印样式 */
    .label-item {
      height: 2.5cm !important;
      border-color: #999 !important;
      background: white !important;
      page-break-inside: avoid;
    }

    .label-item:first-child {
      height: 3.7cm !important;
    }

    .label-item:hover {
      background: white !important;
      border-color: #999 !important;
    }

    .logo-symbol {
      font-size: 16px !important;
    }

    .logo-text {
      font-size: 6px !important;
    }
  }
</style>
