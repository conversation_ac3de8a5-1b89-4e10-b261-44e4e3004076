<template>
  <el-dialog
    v-model="visible"
    title="选择图片"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="image-upload-container">
      <!-- 文件上传区域 -->
      <div class="upload-section">
        <h4>上传本地图片</h4>
        <el-upload
          ref="uploadRef"
          class="image-uploader"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :show-file-list="false"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          accept="image/*"
          drag
        >
          <div class="upload-dragger">
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将图片拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip">
              支持 jpg/png/gif/webp 格式，文件大小不超过 5MB
            </div>
          </div>
        </el-upload>
      </div>

      <!-- URL输入区域 -->
      <div class="url-section">
        <h4>或输入图片地址</h4>
        <el-input v-model="imageUrl" placeholder="请输入图片URL地址" clearable>
          <template #append>
            <el-button @click="previewUrl" :disabled="!imageUrl"
              >预览</el-button
            >
          </template>
        </el-input>
      </div>

      <!-- Base64输入区域 -->
      <div class="base64-section">
        <h4>或粘贴Base64编码</h4>
        <el-input
          v-model="base64Data"
          type="textarea"
          :rows="4"
          placeholder="请粘贴图片的Base64编码（以data:image开头）"
          clearable
        />
      </div>

      <!-- 图片预览区域 -->
      <div v-if="previewImage" class="preview-section">
        <h4>图片预览</h4>
        <div class="preview-container">
          <img :src="previewImage" alt="预览图片" class="preview-image" />
          <div class="preview-info">
            <p>尺寸: {{ imageInfo.width }} × {{ imageInfo.height }}px</p>
            <p>大小: {{ imageInfo.size }}</p>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="!previewImage"
        >
          确定使用
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { ElMessage } from 'element-plus/es';
  import { UploadFilled } from '@element-plus/icons-vue';
  import { getToken } from '@/utils/token-util';
  import {
    fileToBase64,
    getImageDimensions,
    validateImageFile,
    formatFileSize,
    isValidImageUrl,
    parseBase64Info
  } from '@/utils/image-utils';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['update:modelValue', 'confirm']);

  // 响应式数据
  const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
  });

  const uploadRef = ref();
  const imageUrl = ref('');
  const base64Data = ref('');
  const previewImage = ref('');
  const imageInfo = reactive({
    width: 0,
    height: 0,
    size: ''
  });

  // 上传配置
  const uploadUrl = '/api/file/upload'; // 根据您的实际上传接口调整
  const uploadHeaders = computed(() => ({
    Authorization: getToken() || ''
  }));

  // 文件上传前的检查
  const beforeUpload = async (file) => {
    // 使用工具函数验证文件
    const validation = validateImageFile(file, {
      maxSize: 5,
      allowedTypes: [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp'
      ]
    });

    if (!validation.valid) {
      validation.errors.forEach((error) => ElMessage.error(error));
      return false;
    }

    try {
      // 转换为Base64并显示预览
      const base64 = await fileToBase64(file);
      previewImage.value = base64;
      await getImageInfo(base64);

      return true;
    } catch (error) {
      console.error('文件处理失败:', error);
      ElMessage.error('文件处理失败: ' + error.message);
      return false;
    }
  };

  // 上传成功回调
  const handleUploadSuccess = (response) => {
    console.log('上传响应:', response);

    if (response.code === 0) {
      try {
        // 解析返回的数据，data字段是JSON字符串
        const fileData = JSON.parse(response.data);
        console.log('解析后的文件数据:', fileData);

        if (Array.isArray(fileData) && fileData.length > 0) {
          const file = fileData[0];
          // 构建完整的图片访问URL
          const imageUrl = `${window.location.origin}/api/file/inline/${file.id}`;
          console.log('构建的图片URL:', imageUrl);

          previewImage.value = imageUrl;
          getImageInfo(imageUrl);
          ElMessage.success('图片上传成功');
        } else {
          ElMessage.error('上传响应数据格式错误');
        }
      } catch (error) {
        console.error('解析上传响应失败:', error);
        ElMessage.error('解析上传响应失败');
      }
    } else {
      ElMessage.error(response.message || '上传失败');
    }
  };

  // 上传失败回调
  const handleUploadError = (error) => {
    console.error('上传失败:', error);
    ElMessage.error('图片上传失败');
  };

  // 预览URL图片
  const previewUrl = async () => {
    if (!imageUrl.value) return;

    try {
      // 使用工具函数验证URL
      const isValid = await isValidImageUrl(imageUrl.value);
      if (!isValid) {
        ElMessage.error('请输入有效的图片URL地址');
        return;
      }

      previewImage.value = imageUrl.value;
      await getImageInfo(imageUrl.value);
    } catch (error) {
      console.error('URL验证失败:', error);
      ElMessage.error('图片URL验证失败');
    }
  };

  // 获取图片信息
  const getImageInfo = async (src) => {
    try {
      console.log('开始获取图片信息:', src);

      // 使用工具函数获取图片尺寸
      const dimensions = await getImageDimensions(src);
      imageInfo.width = dimensions.width;
      imageInfo.height = dimensions.height;

      console.log('图片尺寸获取成功:', dimensions);

      // 如果是base64，使用工具函数解析信息
      if (src.startsWith('data:image')) {
        const base64Info = parseBase64Info(src);
        imageInfo.size = base64Info ? base64Info.formattedSize : '未知';
      } else {
        // 对于服务器图片，尝试从响应头获取大小信息
        try {
          const response = await fetch(src, { method: 'HEAD' });
          const contentLength = response.headers.get('content-length');
          if (contentLength) {
            imageInfo.size = formatFileSize(parseInt(contentLength));
          } else {
            imageInfo.size = '未知';
          }
        } catch (fetchError) {
          console.warn('无法获取图片大小信息:', fetchError);
          imageInfo.size = '未知';
        }
      }

      console.log('图片信息获取完成:', imageInfo);
    } catch (error) {
      console.error('获取图片信息失败:', error);

      // 提供更详细的错误信息
      let errorMessage = '图片加载失败';
      if (error.message.includes('跨域')) {
        errorMessage = '图片跨域访问被阻止，但仍可正常使用';
        // 跨域错误不清空预览，因为图片实际上可能可以显示
        ElMessage.warning(errorMessage);

        // 设置默认值
        imageInfo.width = 0;
        imageInfo.height = 0;
        imageInfo.size = '未知';
      } else {
        errorMessage = '图片加载失败，请检查地址是否正确';
        ElMessage.error(errorMessage);
        previewImage.value = '';
      }
    }
  };

  // 监听base64输入
  watch(base64Data, async (newVal) => {
    if (newVal && newVal.startsWith('data:image')) {
      try {
        console.log('处理Base64输入:', newVal.substring(0, 50) + '...');
        previewImage.value = newVal;
        await getImageInfo(newVal);
      } catch (error) {
        console.error('Base64处理失败:', error);
        ElMessage.error('Base64数据处理失败: ' + error.message);
      }
    } else if (newVal && newVal.trim()) {
      // 如果输入了内容但不是有效的Base64格式
      ElMessage.warning('请输入有效的Base64编码（以data:image/开头）');
    }
  });

  // 确认选择
  const handleConfirm = () => {
    if (!previewImage.value) {
      ElMessage.error('请先选择或上传图片');
      return;
    }

    emit('confirm', {
      url: previewImage.value,
      width: imageInfo.width,
      height: imageInfo.height,
      size: imageInfo.size
    });

    handleClose();
  };

  // 关闭对话框
  const handleClose = () => {
    // 重置数据
    imageUrl.value = '';
    base64Data.value = '';
    previewImage.value = '';
    imageInfo.width = 0;
    imageInfo.height = 0;
    imageInfo.size = '';

    visible.value = false;
  };
</script>

<style scoped lang="less">
  .image-upload-container {
    .upload-section,
    .url-section,
    .base64-section,
    .preview-section {
      margin-bottom: 24px;

      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .image-uploader {
      .upload-dragger {
        width: 100%;
        height: 120px;
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: border-color 0.2s;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &:hover {
          border-color: #409eff;
        }

        .el-icon--upload {
          font-size: 28px;
          color: #8c939d;
          margin-bottom: 8px;
        }

        .el-upload__text {
          color: #606266;
          font-size: 14px;
          text-align: center;

          em {
            color: #409eff;
            font-style: normal;
          }
        }

        .el-upload__tip {
          color: #909399;
          font-size: 12px;
          text-align: center;
          margin-top: 4px;
        }
      }
    }

    .preview-section {
      .preview-container {
        display: flex;
        gap: 16px;
        align-items: flex-start;

        .preview-image {
          max-width: 200px;
          max-height: 150px;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          object-fit: contain;
        }

        .preview-info {
          flex: 1;

          p {
            margin: 0 0 8px 0;
            color: #606266;
            font-size: 13px;
          }
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
</style>
