<template>
  <div class="coin-preview-container" :style="containerStyle">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">{{
          coinData?.display?.coinName || '钱币详情'
        }}</h1>
        <div class="header-code" v-if="coinData?.coin?.diyCode">
          编码：{{ coinData.coin.diyCode }}
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>

    <!-- 错误信息 -->
    <el-alert
      v-else-if="error"
      :title="error"
      type="error"
      show-icon
      :closable="false"
      class="error-alert"
    />

    <!-- 钱币详情主体 -->
    <div v-else-if="coinData" class="coin-details-main">
      <!-- 钱币图片轮播区域 -->
      <div class="image-carousel-section">
        <template v-if="coinImages.length > 0">
          <!-- 桌面端：轮播展示 -->
          <div class="carousel-container">
            <el-carousel
              :interval="3000"
              height="360px"
              indicator-position="outside"
              arrow="always"
              :autoplay="true"
              :loop="true"
              motion-blur
            >
              <el-carousel-item v-for="(image, index) in coinImages" :key="index">
                <div class="carousel-item-content">
                  <div class="image-label">{{ getImageLabel(index) }}</div>
                  <el-image
                    :src="image"
                    :preview-src-list="coinImages"
                    :initial-index="index"
                    fit="cover"
                    class="carousel-image"
                    :preview-teleported="true"
                  />
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>

          <!-- 移动端：静态展示（点击可预览多图） -->
          <div class="static-image-container">
            <div class="static-image-inner">
              <el-image
                :src="coinImages[0]"
                :preview-src-list="coinImages"
                :initial-index="0"
                fit="contain"
                class="static-image"
                :preview-teleported="true"
              />
              <div class="static-counter" v-if="coinImages.length > 1">共 {{ coinImages.length }} 张</div>
            </div>
          </div>
        </template>
        <div v-else class="no-image">
          <el-icon class="no-image-icon"><Picture /></el-icon>
          <p>暂无图片</p>
        </div>
      </div>

      <!-- 基本信息区域 -->
      <div class="basic-info-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-row">
            <span class="info-label">钱币名称</span>
            <span class="info-value">{{
              coinData?.display?.coinName || '未设置'
            }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">版别</span>
            <span class="info-value">{{
              coinData?.display?.serialNumberWithVersion || '未设置'
            }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">编码</span>
            <span class="info-value code">{{
              coinData?.coin?.diyCode || '未设置'
            }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">发行银行</span>
            <span class="info-value">{{
              coinData?.coin?.issueBank || '中国人民银行'
            }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">冠号</span>
            <span class="info-value">{{
              coinData?.coin?.serialNumber || '未设置'
            }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">年代</span>
            <span class="info-value">{{
              coinData?.coin?.yearInfo || '未设置'
            }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">评级结果</span>
            <span class="info-value grade">{{
              formatGradeScore(
                coinData?.coin?.gradeScore,
                coinData?.coin?.gradeScoreValue
              )
            }}</span>
          </div>

          <div class="info-row" v-if="coinData?.coin?.compensationLevel">
            <span class="info-label">赔付等级</span>
            <span class="info-value">{{
              coinData.coin.compensationLevel
            }}</span>
          </div>

          <div class="info-row full-width" v-if="coinData?.coin?.remark">
            <span class="info-label">备注</span>
            <span class="info-value">{{ coinData.coin.remark }}</span>
          </div>
        </div>
      </div>

      <!-- 底部操作栏：吸底工具条（移动端并排优先，空间不足自动换行） -->
<!--      <div class="action-bar">
        <el-button @click="goBack" class="action-btn" type="default">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <el-button type="primary" @click="refresh" class="action-btn">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>-->
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, onUnmounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  import axios from 'axios';
  import { API_BASE_URL } from '@/config/setting';
  import { Picture, ArrowLeft, Refresh } from '@element-plus/icons-vue';

  const route = useRoute();
  const router = useRouter();

  // 响应式数据
  const loading = ref(false);
  const error = ref('');
  const coinData = ref(null);

  // 设备检测 + 根容器宽度动态样式 + 请求头注入
  // 作者: Augment Agent, 创建时间: 2025-08-08
  const deviceType = ref('desktop');
  const isMobile = computed(() => ['mobile', 'tablet'].includes(deviceType.value));
  let _mql = null;
  let _mqlListener = null;

  function detectDeviceType() {
    if (typeof navigator === 'undefined') return 'desktop';
    const ua = navigator.userAgent || '';
    const isIPhone = /iPhone|iPod/.test(ua);
    const isAndroidMobile = /Android.*Mobile/.test(ua);
    const isAndroid = /Android/.test(ua);
    // iPadOS 13+ 会伪装成 Mac，但有触控点
    const isIPad = /iPad/.test(ua) || (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    if (isIPhone || isAndroidMobile) return 'mobile';
    if (isIPad || (isAndroid && !isAndroidMobile)) return 'tablet';
    if (navigator.userAgentData?.mobile) return 'mobile';
    return 'desktop';
  }

  const setupResponsiveWatchers = () => {
    try {
      deviceType.value = detectDeviceType();
    } catch (_) { deviceType.value = 'desktop'; }
    // 兜底：当 UA 判定为 desktop，但屏宽很小，则按移动端处理
    if (typeof window !== 'undefined' && typeof window.matchMedia === 'function') {
      _mql = window.matchMedia('(max-width: 768px)');
      const applyByWidth = (mq) => {
        if (deviceType.value === 'desktop' && mq?.matches) {
          deviceType.value = 'mobile';
        }
      };
      applyByWidth(_mql);
      _mqlListener = (e) => applyByWidth(e);
      if (typeof _mql.addEventListener === 'function') {
        _mql.addEventListener('change', _mqlListener);
      } else if (typeof _mql.addListener === 'function') {
        _mql.addListener(_mqlListener);
      }
    }
  };

  // 根容器动态样式（JS 判断）
  const containerStyle = computed(() => {
    // 移动/平板：36% 宽度，仅隐藏水平滚动；PC：100% 宽度
    // 作者: Augment Agent, 创建时间: 2025-08-08
    return isMobile.value
      ? { width: '36%', overflowX: 'hidden' }
      : { width: '100%' };
  });

  // 计算属性
  const coinImages = computed(() => {
    const images = [];
    const coin = coinData.value?.coin;

    if (coin?.frontImage) {
      images.push(coin.frontImage);
    }
    if (coin?.backImage) {
      images.push(coin.backImage);
    }

    return images;
  });

  // 方法
  const loadCoinData = async () => {
    const diyCode = route.params.diyCode;
    if (!diyCode) {
      error.value = '缺少送评条码参数';
      return;
    }

    loading.value = true;
    error.value = '';

    try {
      // 直接调用公开接口，无需 token，使用原生 axios
      const res = await axios.get(
        `${API_BASE_URL}/pjosendform/preview/${diyCode}`,
        {
          headers: {
            'X-Client-Device': deviceType.value,
            // 可选：如需后端进一步分析，可传递屏宽与 UA
            'X-Client-Width': typeof window !== 'undefined' ? String(window.innerWidth) : undefined,
            'X-Client-UA': typeof navigator !== 'undefined' ? navigator.userAgent : undefined
          }
        }
      );
      if (res.data.code === 0) {
        coinData.value = res.data.data;
      } else {
        throw new Error(res.data.message);
      }
    } catch (err) {
      error.value = err.message || '加载钱币详情失败';
      ElMessage.error(error.value);
    } finally {
      loading.value = false;
    }
  };

  // 格式化品相分数显示
  const formatGradeScore = (gradeScore, gradeScoreValue) => {
    if (!gradeScore && !gradeScoreValue) {
      return '未评级';
    }

    // 如果有 gradeScoreValue，从 gradeScore 中移除数字部分，然后组合显示
    if (gradeScoreValue && gradeScore) {
      // 移除 gradeScore 中的数字部分，得到文字部分
      const gradeText = gradeScore.replace(gradeScoreValue, '').trim();
      return `${gradeText} ${gradeScoreValue}`;
    }

    // 如果只有 gradeScore，直接显示
    if (gradeScore) {
      return gradeScore;
    }

    // 如果只有 gradeScoreValue，只显示数字
    if (gradeScoreValue) {
      return gradeScoreValue;
    }

    return '未评级';
  };

  // 获取图片标签
  const getImageLabel = (index) => {
    const labels = ['钱币正面', '钱币反面'];
    return labels[index] || `图片${index + 1}`;
  };

  const goBack = () => {
    router.back();
  };

  const refresh = () => {
    loadCoinData();
  };

  // 生命周期
  onMounted(() => {
    // 初始化移动端检测并应用动态样式
    // 作者: Augment Agent, 创建时间: 2025-08-08
    setupResponsiveWatchers();
    loadCoinData();
  });

  onUnmounted(() => {
    try {
      if (_mql) {
        if (typeof _mql.removeEventListener === 'function' && _mqlListener) {
          _mql.removeEventListener('change', _mqlListener);
        } else if (typeof _mql.removeListener === 'function' && _mqlListener) {
          _mql.removeListener(_mqlListener);
        }
      }
    } catch (_) {}
  });
</script>

<style scoped>
  /* 全局容器 */
  .coin-preview-container {
    min-height: 100vh;
    background: #f8f9fa;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      'Helvetica Neue', Arial, sans-serif;
    overflow-x: hidden; /* 防止任何水平溢出导致的滚动 */
    width: 100%;
    margin: 0; /* 去掉左右外边距 */
  }

  /* 页面头部 */
  .page-header {
    background: #fff;
    border-bottom: 1px solid #e9ecef;
    padding: 20px 0;
  }

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: #212529;
    margin: 0 0 8px 0;
  }

  .header-code {
    font-size: 14px;
    color: #6c757d;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }

  /* 加载状态 */
  .loading-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
  }

  /* 错误信息 */
  .error-alert {
    max-width: 1200px;
    margin: 20px auto;
  }

  /* 主要内容区域 */
  .coin-details-main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    padding-bottom: 100px;
  }

  /* 图片轮播区域 */
  .image-carousel-section {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .carousel-container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    box-sizing: border-box; /* 防止内边距导致宽度溢出 */
  }

  /* 新增：静态容器（默认桌面端隐藏，移动端显示） */
  .static-image-container { display: none; }
  .static-image-inner {
    width: 100%;
    height: 360px; /* 桌面高度保持一致，移动端在媒体查询覆盖 */
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    display: grid;
    place-items: center; /* 完全居中 */
    position: relative; /* 供角标定位 */
    margin: 0 auto; /* 当设置百分比宽度时水平居中 */
  }
  .static-image {
    width: 100%;
    height: 100%;
    display: block;
  }
  .static-image :deep(img),
  .static-image :deep(.el-image__inner) {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
    object-position: center center !important;
    display: block !important;
    background: #f8f9fa !important;
  }
  .static-counter {
    position: absolute;
    right: 12px;
    bottom: 12px;
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 12px;
  }

  /* 轮播容器原样式 */
  .carousel-container :deep(.el-carousel) {
    height: 360px; /* 桌面默认高度，移动端在媒体查询内覆盖 */
  }

  /* 轮播项内容 */
  .carousel-item-content {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image-label {
    position: absolute;
    top: 12px;
    left: 12px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 12px;
    font-size: 13px;
    font-weight: 500;
    border-radius: 4px;
    z-index: 2;
  }

  .carousel-image {
    width: 100%;
    height: 100%;
    cursor: pointer;
    transition: all 0.3s ease;
    object-fit: cover;
  }

  .carousel-image:hover {
    transform: scale(1.02);
  }

  /* 轮播组件样式优化 */
  .carousel-container :deep(.el-carousel__container) {
    border-radius: 8px;
    overflow: hidden;
    width: 100%; /* 确保容器本身不超宽 */
  }

  .carousel-container :deep(.el-carousel__item) {
    border-radius: 8px;
    overflow: hidden;
  }

  /* 确保 el-image 组件占满容器 */
  .carousel-container :deep(.el-image) {
    width: 100% !important;
    height: 100% !important;
    display: block !important;
  }

  /* 默认桌面端：图片铺满容器 */
  .carousel-image :deep(img),
  .carousel-image :deep(.el-image__inner) {
    width: 100% !important;
    height: 100% !important;
    border-radius: 8px;
    object-fit: cover !important;
    object-position: center center !important; /* 居中显示，避免偏左上角 */
    display: block !important;
  }

  .carousel-container :deep(.el-carousel__arrow) {
    background-color: rgba(0, 0, 0, 0.6);
    border: none;
    width: 40px;
    height: 40px;
    /* 提升移动端触控面积 */
    touch-action: manipulation;
  }

  .carousel-container :deep(.el-carousel__arrow:hover) {
    background-color: rgba(0, 0, 0, 0.8);
  }

  .carousel-container :deep(.el-carousel__indicators) {
    margin-top: 16px;
  }

  .carousel-container :deep(.el-carousel__indicator) {
    padding: 8px 4px;
  }

  .carousel-container :deep(.el-carousel__button) {
    width: 12px;
    height: 4px;
    border-radius: 2px;
    background-color: #c0c4cc;
  }

  .carousel-container
    :deep(.el-carousel__indicator.is-active .el-carousel__button) {
    background-color: #007bff;
  }

  .no-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #6c757d;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
  }

  .no-image-icon {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.5;
  }

  .no-image p {
    margin: 0;
    font-size: 16px;
  }

  /* 基本信息区域 */
  .basic-info-section {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #212529;
    margin: 0 0 20px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid #007bff;
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 0;
  }

  .info-row {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
  }

  .info-row:hover {
    background-color: #f8f9fa;
    padding-left: 8px;
    padding-right: 8px;
    border-radius: 4px;
  }

  .info-row:last-child {
    border-bottom: none;
  }

  .info-row.full-width {
    grid-column: 1 / -1;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .info-label {
    font-size: 14px;
    font-weight: 600;
    color: #6c757d;
    min-width: 100px;
    margin-right: 16px;
  }

  .info-row.full-width .info-label {
    margin-right: 0;
    margin-bottom: 4px;
  }

  .info-value {
    font-size: 15px;
    color: #212529;
    flex: 1;
    word-break: break-word;
  }

  .info-value.code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 13px;
    display: inline-block;
  }

  .info-value.grade {
    font-weight: 600;
    color: #28a745;
  }

  /* 底部操作栏 */
  .action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    border-top: 1px solid #e9ecef;
    padding: 12px 16px; /* 稍减内边距以提升小屏可用空间 */
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap; /* 空间不足时自动换行 */
  }

  .action-btn {
    padding: 10px 18px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
  }

  .action-btn:hover {
    transform: translateY(-1px);
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    /* 根据设备屏宽缩小整体容器宽度，确保内部图片完整显示 */
    /* 使用 JS 动态控制根容器宽度，移除此处宽度设置以避免冲突（Confirmed via 寸止） */

    .coin-details-main {
      padding: 16px;
      padding-bottom: 100px;
    }

    .image-carousel-section,
    .basic-info-section {
      padding: 20px;
    }

    /* 移动端修复：确保容器不溢出，图片完整显示 */
    .carousel-container {
      max-width: 100% !important;
      width: 100% !important;
    }
    .image-carousel-section {
      overflow: hidden;
    }

    /* 确保轮播容器高度合适 */
    .carousel-container :deep(.el-carousel) {
      height: 280px !important;
    }

    /* 确保轮播项内容占满容器 */
    .carousel-item-content {
      width: 100% !important;
      height: 100% !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }

    /* 确保图片组件占满容器 */
    .carousel-image {
      width: 100% !important;
      height: 100% !important;
    }

    /* 关键：强制移动端图片完整显示，不被裁剪 */
    .carousel-image :deep(img) {
      width: 100% !important;
      height: 100% !important;
      object-fit: contain !important;
      background: #f8f9fa !important;
    }

    /* 移动端：改为单列，并将标签与内容垂直排列，增强可读性 */
    .info-grid {
      grid-template-columns: 1fr;
    }
    .info-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 6px;
    }

    .info-label {
      min-width: auto;
      margin-right: 0;
      margin-bottom: 4px;
    }

    .action-bar {
      padding: 10px 12px;
    }
    .action-btn {
      min-width: 120px;
      flex: 1 1 auto;
    }
  }

  /* 机型/屏宽细化映射（越小屏，容器越窄） */
  /* 统一基于屏宽的动态分级（示例：大屏 50vw，小屏逐级收窄） */










  @media (max-width: 480px) {
    .static-image-inner { height: 240px; }
  }

  @media (max-width: 480px) {
    .page-title {
      font-size: 20px;
    }

    .image-carousel-section {
      padding: 16px;
    }

    .carousel-container {
      max-width: 100% !important;
      width: 100% !important;
    }

    /* 小屏轮播高度调整 */
    .carousel-container :deep(.el-carousel) {
      height: 240px !important;
    }

    /* 小屏图片完全适配，适度内边距避免贴边 */
    .carousel-image :deep(img),
    .carousel-image :deep(.el-image__inner) {
      padding: 8px !important;
      box-sizing: border-box !important;
      object-fit: contain !important;
      object-position: center center !important;
    }

    .carousel-container :deep(.el-carousel__arrow) {
      width: 32px;
      height: 32px;
    }

    .image-label {
      font-size: 12px;
      padding: 4px 8px;
      top: 8px;
      left: 8px;
    }

    .basic-info-section {
      padding: 16px;
    }

    .section-title {
      font-size: 16px;
    }

    .info-label {
      font-size: 13px;
    }

    .info-value {
      font-size: 14px;
    }

    /* 小屏下操作按钮可堆叠为两行，但仍保持足够点击面积 */
    .action-bar {
      row-gap: 8px;
    }

    .static-image-inner { height: 240px; }
  }

  /* 超小屏幕优化 */
  @media (max-width: 360px) {
    .carousel-container :deep(.el-carousel) {
      height: 200px !important;
    }

    .carousel-image :deep(img),
    .carousel-image :deep(.el-image__inner) {
      padding: 4px !important;
      object-fit: contain !important;
      object-position: center center !important;
    }

    .image-label {
      font-size: 11px;
      padding: 3px 6px;
    }

    .static-image-inner { height: 200px; width: 42%; }
  }
</style>
