# 常用模式和最佳实践

- 批量打印预览功能修复最佳实践：1. 打印时必须完整复制预览的CSS样式，特别是hiprint相关样式；2. 使用相同的布局计算方法(getLabelItemStyle, getPrintPageStyle)确保一致性；3. 打印媒体查询需要强制应用关键样式(!important)；4. 添加打印预览功能帮助用户验证效果；5. 保持分页逻辑完全一致，确保一页多签布局正确传递到打印输出
- API 层统一使用 src/utils/request.js 封装 axios：baseURL=API_BASE_URL（来自 VITE_API_URL）；请求拦截器自动注入 Authorization token 与 roleId；GET 请求将 params 转换为查询字符串；响应拦截器处理 401 弹窗与回登录、自动续期 token（从响应头 authorization）；提供 streamRequest 以 fetch 实现流式/较低层的请求控制，同步续期与401处理逻辑。
- 常用模式/最佳实践：1) 路由守卫 + 动态注入（首次拿菜单后 addRoute 并 replace）；2) 权限指令（v-role/v-any-role/v-permission/v-any-permission）进行按钮级控制；3) axios 封装 + 401 统一处理 + token 续期；4) fetch streamRequest 用于流式与下载场景；5) Pinia 负责用户/主题/页签，Vuex 只用于特定跨页共享并 sessionStorage 持久化；6) i18n + setPageTitle 统一标题；7) Vite 按需引入 + gzip + SCSS 变量注入；8) 开发代理 /api → http://localhost:7070。
