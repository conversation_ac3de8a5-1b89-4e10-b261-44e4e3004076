# Patterns（常用模式与最佳实践）

- 平台与模块
  - Spring Boot 3 + Java 17
  - 模块：payne-auth、payne-upms、payne-common、payne-server、payne-generator
- 安全与令牌
  - JwtUtil 发放/解析；响应头 authorization 续期
  - Redis 保存 token/refreshToken/会话
- 数据访问
  - MyBatis-Plus：CRUD、分页、Wrapper 条件
- 代码生成器
  - 暴露 /generator/tables、/generator/columns 提供库表/字段元数据
- 配置
  - application.yml 指定 server.port=7070；profiles 切换 dev/prod
- 对前端契约
  - 统一 /api 前缀；返回 { code, message, data }；401/续期语义一致

