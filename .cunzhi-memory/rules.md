# Rules（开发规范与规则）

- API 前缀与返回模型
  - 统一前缀：/api
  - 统一返回：{ code, message, data }；成功 code=0
  - 分页返回字段：list、count
- 鉴权与续期
  - 鉴权失败返回 401（由前端统一弹窗并跳转登录）
  - 响应头 authorization 下发新 token，用于续期
  - 从请求头读取 Authorization 与自定义 roleId 用于鉴权/角色切换
- 菜单与权限数据契约（供前端动态路由使用）
  - 提供 authorities 列表；按钮权限标识：menuType=2
  - 菜单项应包含字段：path、component、title、icon、hide、meta（JSON 字符串，包含 layout 等）
- 端口与环境配置
  - 默认端口：server.port=7070
  - 多环境配置：application-{dev,prod}.yml；application.yml 中 spring.profiles.active 控制
  - app.preview.base-url 与前端保持一致（dev 默认 http://localhost:5173）
- 错误码与异常处理
  - 业务异常统一转为 { code!=0, message }
  - 使⽤统一异常处理器输出一致格式
- 文件/流式接口
  - 导出/大文件使用流式响应；合理配置超时与内存使用

（与前端约定：前端通过 Vite 代理以 /api 访问后端 7070 端⼝；401/续期语义需保持一致）

