# 开发规范和规则

- 前端架构：路由采用静态+动态（基于后端返回菜单 authorities 构建）双轨；动态路由通过 userStore.fetchUserInfo 获取菜单后调用 getMenuRoutes 铺平并按 layout 元信息决定是否包裹外层 Layout；路由守卫统一处理登录校验（WHITE_LIST 白名单支持前缀匹配）、标题设定（支持 meta.titles 指定 fullPath 定制标题）、NProgress 进度条，首次登录后动态注册路由并 replace，以避免重复注册。
- 路由规范：静态路由仅放公共页（/login、/coin-preview/:diyCode、/label-processing-test、404）；动态路由基于后端 authorities 生成（menuToRoutes + eachTree），按 meta.layout 区分是否包裹 Layout；白名单支持前缀匹配（includes + startsWith），未登录重定向 /login 并保留 from；标题统一由 meta.titles[fullPath] 或 i18n 计算；首次登录后动态注册路由并 replace，防止重复注册；公共测试页（如 /label-processing-test）应 meta.layout=false 且加入 WHITE_LIST。
