# 项目上下文信息

- dev-platform-ui 前端项目路径: /Users/<USER>/project/ele-admin/dev-platform-ui；技术栈：Vue 3 + Vite 5、Pinia + Vuex（持久化）、Vue Router、Element Plus + Ele Admin Plus、ECharts、i18n；开发代理 /api -> http://localhost:7070；入口 src/main.js，构建配置 vite.config.js。
- dev-platform 后端项目路径: /Users/<USER>/project/ele-admin/dev-platform；技术栈：Java 17、Spring Boot 3.5.3、Maven 多模块（payne-auth、payne-upms、payne-common、payne-server、payne-generator）、MyBatis-Plus、Redisson、JWT、Captcha、Oracle/MySQL/达梦驱动等。
- 状态管理：Pinia 模块位于 src/store/modules（theme、user）。theme 持久化至 localStorage（THEME_CACHE_NAME），提供页签和主题配置；user 负责用户信息、权限与菜单，menus 从 authorities 过滤、树化、格式化生成；同时存在 Vuex store（src/store/store-vuex.js）使用 vuex-persistedstate 将 sharedZizhuData/sharedWorkFlowData/sharedPersonData 持久化到 sessionStorage，主要服务于工作流/资助/人员详情等特定模块的跨页共享。
- 环境与构建：.env / .env.development / .env.production 定义 VITE_API_URL（默认 /api，生产/开发示例注释的直连地址可用）；vite.config.js：开发代理 /api -> http://localhost:7070；生产按需引入 Element Plus/EleAdmin 组件、gzip 压缩、SCSS 变量注入、optimizeDeps 预构建 ECharts/XLSX 等、build chunkSizeWarningLimit=2000；开发环境通过 alias['./as-needed']='./global-import' 实现全局安装 UI 组件。
- 模块职责：views 按业务域组织（bank-note、system、base-code 等）；components 提供可复用通用组件（RouterLayout、DictData、上传、编辑器等）；utils 提供权限、token、page-title、hiprint 打印配置、通用工具；特别页面 /label-processing-test 为标签处理功能的开发/调试页面，消耗 request.get('/labels/health') 与 LabelArchiveUpload 组件，用于验证 API 可用性与组件集成。
- 项目上下文：Vue 3 + Vite 5 + Pinia/Vuex + Router + Element Plus + EleAdmin；入口 src/main.js；路由位于 src/router（routes.js 静态、index.js 守卫与动态注入）；Pinia 模块 src/store/modules/{user,theme}；Vuex src/store/store-vuex.js；API 封装 src/utils/request.js；环境变量 .env* 中 VITE_API_URL；构建 vite.config.js；测试专用路由 /label-processing-test（meta.layout=false，WHITE_LIST 放行）。
- 用户切换到前端项目 dev-platform-ui，之前在后端项目 dev-platform 中修复了 Tesseract OCR 的 UnsatisfiedLinkError 问题。当前讨论二维码识别问题：localhost 地址生成的二维码无法被 BufferedImageLuminanceSource 正确识别，但生产环境地址可以。问题根源是前端使用在线 API 生成二维码与后端 QrCodeUtil 生成方式不同导致的图像质量差异。
