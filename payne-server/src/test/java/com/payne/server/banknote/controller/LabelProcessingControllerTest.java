package com.payne.server.banknote.controller;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 标签处理控制器测试类
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@SpringBootTest
@ActiveProfiles("test")
public class LabelProcessingControllerTest {

    @Test
    public void contextLoads() {
        // 简单的上下文加载测试
        // 如果Spring能够成功启动，说明所有的Bean都能正确注入
    }
}
