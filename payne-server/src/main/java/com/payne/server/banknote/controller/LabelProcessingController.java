package com.payne.server.banknote.controller;

import com.payne.core.web.ApiResult;
import com.payne.core.web.BaseController;
import com.payne.server.banknote.service.LabelProcessingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 钱币标签图片批量处理控制器
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Slf4j
@RestController
@RequestMapping("/api/labels")
public class LabelProcessingController extends BaseController {

    @Resource
    private LabelProcessingService labelProcessingService;

    /**
     * 健康检查端点
     */
    @GetMapping("/health")
    public ApiResult<Map<String, Object>> health() {
        Map<String, Object> status = new HashMap<>();
        status.put("service", "LabelProcessingService");
        status.put("status", "UP");
        status.put("timestamp", System.currentTimeMillis());
        return success("服务正常", status);
    }

    /**
     * 批量处理上传的钱币标签图片压缩包
     * 
     * @param file 上传的 .zip 压缩包文件
     * @return 处理结果摘要
     */
    @PostMapping("/process-archive")
    public ApiResult<?> processArchive(@RequestParam("file") MultipartFile file) {
        try {
            // 参数验证
            if (file == null || file.isEmpty()) {
                return fail("请选择要上传的压缩包文件");
            }

            // 文件类型验证
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".zip")) {
                return fail("只支持 .zip 格式的压缩包文件");
            }

            // 文件大小验证（限制为 100MB）
            if (file.getSize() > 100 * 1024 * 1024) {
                return fail("压缩包文件大小不能超过 100MB");
            }

            log.info("开始处理钱币标签图片压缩包: {}, 大小: {} bytes", originalFilename, file.getSize());

            // 调用服务处理压缩包
            Map<String, Object> result = labelProcessingService.processZipArchive(file);

            log.info("钱币标签图片压缩包处理完成: {}", result);

            return success("处理完成", result);

        } catch (Exception e) {
            log.error("处理钱币标签图片压缩包时发生错误", e);
            return fail("处理失败: " + e.getMessage());
        }
    }
}
