package com.payne.server.banknote.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 钱币标签图片批量处理服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
public interface LabelProcessingService {

    /**
     * 处理上传的钱币标签图片压缩包
     * 
     * @param file 上传的 .zip 压缩包文件
     * @return 处理结果摘要，包含：
     *         - totalPairs: 总配对数
     *         - successCount: 成功处理数
     *         - failureCount: 失败处理数
     *         - failures: 失败详情列表
     *         - processingTime: 处理耗时（毫秒）
     */
    Map<String, Object> processZipArchive(MultipartFile file);
}
