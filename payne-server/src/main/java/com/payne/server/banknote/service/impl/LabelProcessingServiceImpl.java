package com.payne.server.banknote.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.zxing.*;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.common.HybridBinarizer;
import com.payne.core.web.GridFsService;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.service.LabelProcessingService;
import com.payne.server.banknote.service.PjOSendformItemService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.tess4j.ITesseract;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 钱币标签图片批量处理服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Slf4j
@Service
public class LabelProcessingServiceImpl implements LabelProcessingService {

    @Resource
    private GridFsService gridFsService;

    @Resource
    private PjOSendformItemService pjOSendformItemService;

    // QR码识别器
    private final MultiFormatReader qrCodeReader = new MultiFormatReader();
    
    // OCR识别器（通过配置注入）
    @Resource
    private ITesseract tesseract;

    // 支持的图片格式
    private static final Set<String> SUPPORTED_IMAGE_EXTENSIONS = Set.of("jpg", "jpeg", "png", "bmp", "gif");
    
    // ZK开头的送评单号正则表达式
    private static final Pattern DIY_CODE_PATTERN = Pattern.compile("(ZK\\d+)");

    @Override
    public Map<String, Object> processZipArchive(MultipartFile file) {
        long startTime = System.currentTimeMillis();
        
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> failures = new ArrayList<>();
        int totalPairs = 0;
        int successCount = 0;
        int failureCount = 0;

        try {
            // 第一步：解压ZIP文件并按前缀分组
            Map<String, Map<String, byte[]>> groupedImages = extractAndGroupImages(file);
            
            if (groupedImages.isEmpty()) {
                result.put("totalPairs", 0);
                result.put("successCount", 0);
                result.put("failureCount", 0);
                result.put("failures", List.of(Map.of("error", "压缩包中未找到有效的图片文件")));
                result.put("processingTime", System.currentTimeMillis() - startTime);
                return result;
            }

            totalPairs = groupedImages.size();
            log.info("从压缩包中提取到 {} 组图片配对", totalPairs);

            // 第二步：处理每组图片配对
            for (Map.Entry<String, Map<String, byte[]>> entry : groupedImages.entrySet()) {
                String groupKey = entry.getKey();
                Map<String, byte[]> imagePair = entry.getValue();

                // 从 groupKey 中提取原始前缀（去掉 _pairX 后缀）
                String originalPrefix = groupKey.substring(0, groupKey.lastIndexOf("_pair"));

                try {
                    boolean success = processSingleImagePair(originalPrefix, imagePair);
                    if (success) {
                        successCount++;
                        log.debug("成功处理图片配对: {} ({})", originalPrefix, groupKey);
                    } else {
                        failureCount++;
                        failures.add(Map.of(
                            "prefix", originalPrefix,
                            "error", "处理图片配对时发生未知错误"
                        ));
                    }
                } catch (Exception e) {
                    failureCount++;
                    failures.add(Map.of(
                        "prefix", originalPrefix,
                        "error", "处理图片配对时发生异常: " + e.getMessage()
                    ));
                    log.error("处理图片配对 {} ({}) 时发生异常", originalPrefix, groupKey, e);
                }
            }

        } catch (Exception e) {
            log.error("处理ZIP压缩包时发生异常", e);
            failures.add(Map.of("error", "解压ZIP文件时发生异常: " + e.getMessage()));
        }

        // 构建返回结果
        result.put("totalPairs", totalPairs);
        result.put("successCount", successCount);
        result.put("failureCount", failureCount);
        result.put("failures", failures);
        result.put("processingTime", System.currentTimeMillis() - startTime);

        return result;
    }

    /**
     * 解压ZIP文件并按前缀分组图片
     */
    private Map<String, Map<String, byte[]>> extractAndGroupImages(MultipartFile file) throws IOException {
        Map<String, Map<String, byte[]>> groupedImages = new HashMap<>();

        try (ZipInputStream zipInputStream = new ZipInputStream(file.getInputStream())) {
            ZipEntry entry;
            
            while ((entry = zipInputStream.getNextEntry()) != null) {
                if (entry.isDirectory()) {
                    continue;
                }

                String fileName = entry.getName();
                
                // 提取文件名（去除路径）
                if (fileName.contains("/")) {
                    fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
                }
                
                // 检查是否为支持的图片格式
                if (!isImageFile(fileName)) {
                    continue;
                }

                // 解析文件名格式：[前缀]_[编号].jpg
                String[] parts = fileName.split("_");
                if (parts.length != 2) {
                    log.warn("跳过不符合命名规则的文件: {}", fileName);
                    continue;
                }

                String prefix = parts[0];
                String numberPart = parts[1].substring(0, parts[1].lastIndexOf('.'));

                try {
                    int number = Integer.parseInt(numberPart);

                    // 读取图片字节数据
                    byte[] imageBytes = zipInputStream.readAllBytes();

                    // 计算配对组号（1,2为第1组，3,4为第2组，以此类推）
                    int pairGroup = (number + 1) / 2;
                    String groupKey = prefix + "_pair" + pairGroup;

                    // 按配对分组
                    groupedImages.computeIfAbsent(groupKey, k -> new HashMap<>());

                    // 奇数为正面，偶数为反面
                    String side = (number % 2 == 1) ? "front" : "back";
                    groupedImages.get(groupKey).put(side, imageBytes);

                    log.debug("提取图片: {} -> {} ({})", fileName, groupKey, side);
                    
                } catch (NumberFormatException e) {
                    log.warn("跳过编号格式错误的文件: {}", fileName);
                }
            }
        }

        // 过滤掉不完整的配对（必须同时有正面和反面）
        groupedImages.entrySet().removeIf(entry -> {
            Map<String, byte[]> pair = entry.getValue();
            boolean isComplete = pair.containsKey("front") && pair.containsKey("back");
            if (!isComplete) {
                log.warn("移除不完整的图片配对: {} (缺少正面或反面图片)", entry.getKey());
            }
            return !isComplete;
        });

        return groupedImages;
    }

    /**
     * 检查是否为支持的图片文件
     */
    private boolean isImageFile(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return false;
        }
        
        String extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
        return SUPPORTED_IMAGE_EXTENSIONS.contains(extension);
    }

    /**
     * 处理单个图片配对
     */
    private boolean processSingleImagePair(String prefix, Map<String, byte[]> imagePair) {
        try {
            byte[] frontImageBytes = imagePair.get("front");
            byte[] backImageBytes = imagePair.get("back");

            // 第一步：从正面图片提取送评单号
            String diyCode = extractDiyCodeFromImage(frontImageBytes);
            if (!StringUtils.hasText(diyCode)) {
                log.warn("无法从正面图片提取送评单号，跳过配对: {}", prefix);
                return false;
            }

            log.info("从图片配对 {} 中提取到送评单号: {}", prefix, diyCode);

            // 第二步：上传图片到GridFS
            String frontImageUrl = uploadImageToGridFS(frontImageBytes, prefix + "_front.jpg");
            String backImageUrl = uploadImageToGridFS(backImageBytes, prefix + "_back.jpg");

            if (frontImageUrl == null || backImageUrl == null) {
                log.error("上传图片到GridFS失败，跳过配对: {}", prefix);
                return false;
            }

            // 第三步：更新数据库中的钱币记录
            return updateCoinRecord(diyCode, frontImageUrl, backImageUrl);

        } catch (Exception e) {
            log.error("处理图片配对 {} 时发生异常", prefix, e);
            return false;
        }
    }

    /**
     * 从图片中提取送评单号
     */
    private String extractDiyCodeFromImage(byte[] imageBytes) {
        try {
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageBytes));
            if (image == null) {
                log.warn("无法读取图片数据");
                return null;
            }

            // 优先尝试QR码识别
            String diyCode = extractDiyCodeFromQR(image);
            if (StringUtils.hasText(diyCode)) {
                return diyCode;
            }

            // 备用方案：OCR识别
            return extractDiyCodeFromOCR(image);

        } catch (Exception e) {
            log.error("从图片提取送评单号时发生异常", e);
            return null;
        }
    }

    /**
     * 通过QR码识别提取送评单号
     */
    private String extractDiyCodeFromQR(BufferedImage image) {
        try {
            LuminanceSource source = new BufferedImageLuminanceSource(image);
            BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));
            
            Result result = qrCodeReader.decode(bitmap);
            String qrContent = result.getText();
            
            log.debug("QR码内容: {}", qrContent);
            
            // 从URL中提取送评单号，例如：http://.../coin-preview/ZK25080001
            if (qrContent.contains("/coin-preview/")) {
                String diyCode = qrContent.substring(qrContent.lastIndexOf("/") + 1);
                if (diyCode.startsWith("ZK")) {
                    return diyCode;
                }
            }
            
        } catch (NotFoundException e) {
            log.debug("图片中未找到QR码");
        } catch (Exception e) {
            log.warn("QR码识别失败", e);
        }
        
        return null;
    }

    /**
     * 通过OCR识别提取送评单号
     */
    private String extractDiyCodeFromOCR(BufferedImage image) {
        try {
            // 检查OCR识别器是否可用
            if (tesseract == null) {
                log.warn("Tesseract OCR未正确配置，跳过OCR识别");
                return null;
            }

            String ocrText = tesseract.doOCR(image);
            log.debug("OCR识别文本: {}", ocrText);

            // 使用正则表达式查找ZK开头的送评单号
            Matcher matcher = DIY_CODE_PATTERN.matcher(ocrText);
            if (matcher.find()) {
                return matcher.group(1);
            }

        } catch (Exception e) {
            log.warn("OCR识别失败", e);
        }

        return null;
    }

    /**
     * 上传图片到GridFS
     */
    private String uploadImageToGridFS(byte[] imageBytes, String fileName) {
        try {
            // 创建临时MultipartFile对象
            MultipartFile tempFile = new ByteArrayMultipartFile(imageBytes, fileName);
            
            // 上传到GridFS
            List<com.payne.core.web.FileInfo> fileInfos = gridFsService.save(null, tempFile);
            
            if (fileInfos != null && !fileInfos.isEmpty()) {
                String fileId = fileInfos.get(0).getId();
                return "/api/file/inline/" + fileId;
            }
            
        } catch (Exception e) {
            log.error("上传图片到GridFS失败: {}", fileName, e);
        }
        
        return null;
    }

    /**
     * 更新钱币记录的图片字段
     */
    private boolean updateCoinRecord(String diyCode, String frontImageUrl, String backImageUrl) {
        try {
            // 根据送评单号查找钱币记录
            LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PjOSendformItem::getDiyCode, diyCode);
            
            List<PjOSendformItem> coins = pjOSendformItemService.list(wrapper);
            
            if (coins.isEmpty()) {
                log.warn("未找到送评单号为 {} 的钱币记录", diyCode);
                return false;
            }
            
            // 如果有多条记录，更新第一条并记录警告
            PjOSendformItem coin = coins.get(0);
            if (coins.size() > 1) {
                log.warn("发现重复的送评单号: {}, 共{}条记录，已更新第一条记录", diyCode, coins.size());
            }
            
            // 更新图片字段
            coin.setFrontImage(frontImageUrl);
            coin.setBackImage(backImageUrl);
            
            boolean success = pjOSendformItemService.updateById(coin);
            
            if (success) {
                log.info("成功更新钱币记录图片: {} -> 正面: {}, 反面: {}", diyCode, frontImageUrl, backImageUrl);
            } else {
                log.error("更新钱币记录失败: {}", diyCode);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("更新钱币记录时发生异常: {}", diyCode, e);
            return false;
        }
    }

    /**
     * 内部类：字节数组MultipartFile实现
     */
    private static class ByteArrayMultipartFile implements MultipartFile {
        private final byte[] content;
        private final String name;

        public ByteArrayMultipartFile(byte[] content, String name) {
            this.content = content;
            this.name = name;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return name;
        }

        @Override
        public String getContentType() {
            return "image/jpeg";
        }

        @Override
        public boolean isEmpty() {
            return content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() {
            return content;
        }

        @Override
        public java.io.InputStream getInputStream() {
            return new ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
            throw new UnsupportedOperationException("transferTo not supported");
        }
    }
}
