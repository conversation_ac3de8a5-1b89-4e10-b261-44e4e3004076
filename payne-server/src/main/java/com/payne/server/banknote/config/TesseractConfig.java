package com.payne.server.banknote.config;

import lombok.extern.slf4j.Slf4j;
import net.sourceforge.tess4j.ITesseract;
import net.sourceforge.tess4j.Tesseract;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.io.File;

// 引入JNA以便在代码层设置原生库搜索路径（libtesseract/liblept）
import com.sun.jna.NativeLibrary;

/**
 * Tesseract OCR 配置类
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "tesseract")
public class TesseractConfig {

    /**
     * Tesseract数据路径
     */
    private String dataPath;

    /**
     * 语言包，默认为英文
     */
    private String language = "eng";

    /**
     * OCR引擎模式
     */
    private int ocrEngineMode = 1;

    /**
     * 页面分割模式
     */
    private int pageSegMode = 3;

    /**
     * 可选：本机原生库目录（用于JNA查找 libtesseract/liblept），例如：
     * macOS(Apple Silicon): /opt/homebrew/lib
     * macOS(Intel): /usr/local/lib
     * Linux: /usr/lib 或 /usr/local/lib
     */
    private String libraryPath;

    @Bean
    public ITesseract tesseract() {
        // 在 Bean 初始化前设置 JNA 本机库搜索路径，尽量避免 UnsatisfiedLinkError
        try {
            if (StringUtils.hasText(libraryPath)) {
                // 为 tesseract 和 leptonica 均添加搜索路径（两者都需要）
                // {{ Confirmed via 寸止 }} 由用户选择“代码层自动注入”方案
                NativeLibrary.addSearchPath("tesseract", libraryPath);
                NativeLibrary.addSearchPath("lept", libraryPath);
                log.info("JNA 原生库搜索路径已添加: {}", libraryPath);
            }
        } catch (Throwable t) {
            // 安全兜底：即使设置失败也不影响后续逻辑
            log.warn("设置 JNA 原生库搜索路径失败: {}", t.getMessage());
        }

        Tesseract tesseract = new Tesseract();

        try {
            // 设置数据路径
            if (StringUtils.hasText(dataPath)) {
                tesseract.setDatapath(dataPath);
                log.info("Tesseract数据路径设置为: {}", dataPath);
            } else {
                // 尝试自动检测常见的Tesseract安装路径
                String[] commonPaths = {
                    "/usr/share/tesseract-ocr/4.00/tessdata",
                    "/usr/share/tesseract-ocr/tessdata",
                    "/usr/local/share/tessdata",
                    "/opt/homebrew/share/tessdata",
                    "C:/Program Files/Tesseract-OCR/tessdata",
                    "C:/Program Files (x86)/Tesseract-OCR/tessdata"
                };

                for (String path : commonPaths) {
                    if (new File(path).exists()) {
                        tesseract.setDatapath(path);
                        log.info("自动检测到Tesseract数据路径: {}", path);
                        break;
                    }
                }
            }

            // 设置语言
            tesseract.setLanguage(language);

            // 设置OCR引擎模式
            tesseract.setOcrEngineMode(ocrEngineMode);

            // 设置页面分割模式
            tesseract.setPageSegMode(pageSegMode);

            log.info("Tesseract OCR 初始化成功 - 语言: {}, 引擎模式: {}, 分割模式: {}",
                    language, ocrEngineMode, pageSegMode);

        } catch (Exception e) {
            log.warn("Tesseract OCR 初始化失败，OCR功能将不可用: {}", e.getMessage());
            // 返回一个空的实现，避免注入失败
            return new Tesseract() {
                @Override
                public String doOCR(java.awt.image.BufferedImage bi) {
                    throw new UnsupportedOperationException("Tesseract OCR 未正确配置");
                }
            };
        }

        return tesseract;
    }

    // Getter and Setter methods
    public String getDataPath() {
        return dataPath;
    }

    public void setDataPath(String dataPath) {
        this.dataPath = dataPath;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public int getOcrEngineMode() {
        return ocrEngineMode;
    }

    public void setOcrEngineMode(int ocrEngineMode) {
        this.ocrEngineMode = ocrEngineMode;
    }

    public int getPageSegMode() {
        return pageSegMode;
    }

    public void setPageSegMode(int pageSegMode) {
        this.pageSegMode = pageSegMode;
    }

    public String getLibraryPath() {
        return libraryPath;
    }

    public void setLibraryPath(String libraryPath) {
        this.libraryPath = libraryPath;
    }
}
