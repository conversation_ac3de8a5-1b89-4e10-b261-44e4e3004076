[2m2025-08-10 22:10:43.762[0;39m [31mERROR[0;39m [35m39681[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-10 22:29:11.096[0;39m [31mERROR[0;39m [35m41744[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-10 22:31:17.279[0;39m [31mERROR[0;39m [35m41744[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.core.handler.GlobalExceptionHandler [0;39m [2m:[0;39m Handler dispatch failed: java.lang.UnsatisfiedLinkError: Unable to load library 'tesseract':
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/System/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache), '/Library/Frameworks/tesseract.framework/tesseract' (no such file)
Native library (darwin-aarch64/libtesseract.dylib) not found in resource path (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/com/google/zxing/core/3.5.3/core-3.5.3.jar:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.5.3/javase-3.5.3.jar:/Users/<USER>/.m2/repository/com/beust/jcommander/1.82/jcommander-1.82.jar:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/tess4j/tess4j/5.9.0/tess4j-5.9.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox/3.0.1/pdfbox-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-io/3.0.1/pdfbox-io-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/fontbox/3.0.1/fontbox-3.0.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-tools/3.0.1/pdfbox-tools-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-debugger/3.0.1/pdfbox-debugger-3.0.1.jar:/Users/<USER>/.m2/repository/info/picocli/picocli/4.7.5/picocli-4.7.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/jbig2-imageio/3.0.4/jbig2-imageio-3.0.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/lept4j/lept4j/1.18.2/lept4j-1.18.2.jar:/Users/<USER>/.m2/repository/org/jboss/jboss-vfs/3.2.17.Final/jboss-vfs-3.2.17.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar)

jakarta.servlet.ServletException: Handler dispatch failed: java.lang.UnsatisfiedLinkError: Unable to load library 'tesseract':
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/System/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache), '/Library/Frameworks/tesseract.framework/tesseract' (no such file)
Native library (darwin-aarch64/libtesseract.dylib) not found in resource path (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/com/google/zxing/core/3.5.3/core-3.5.3.jar:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.5.3/javase-3.5.3.jar:/Users/<USER>/.m2/repository/com/beust/jcommander/1.82/jcommander-1.82.jar:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/tess4j/tess4j/5.9.0/tess4j-5.9.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox/3.0.1/pdfbox-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-io/3.0.1/pdfbox-io-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/fontbox/3.0.1/fontbox-3.0.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-tools/3.0.1/pdfbox-tools-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-debugger/3.0.1/pdfbox-debugger-3.0.1.jar:/Users/<USER>/.m2/repository/info/picocli/picocli/4.7.5/picocli-4.7.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/jbig2-imageio/3.0.4/jbig2-imageio-3.0.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/lept4j/lept4j/1.18.2/lept4j-1.18.2.jar:/Users/<USER>/.m2/repository/org/jboss/jboss-vfs/3.2.17.Final/jboss-vfs-3.2.17.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.payne.auth.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.UnsatisfiedLinkError: Unable to load library 'tesseract':
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/System/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache), '/Library/Frameworks/tesseract.framework/tesseract' (no such file)
Native library (darwin-aarch64/libtesseract.dylib) not found in resource path (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/com/google/zxing/core/3.5.3/core-3.5.3.jar:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.5.3/javase-3.5.3.jar:/Users/<USER>/.m2/repository/com/beust/jcommander/1.82/jcommander-1.82.jar:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/tess4j/tess4j/5.9.0/tess4j-5.9.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox/3.0.1/pdfbox-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-io/3.0.1/pdfbox-io-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/fontbox/3.0.1/fontbox-3.0.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-tools/3.0.1/pdfbox-tools-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-debugger/3.0.1/pdfbox-debugger-3.0.1.jar:/Users/<USER>/.m2/repository/info/picocli/picocli/4.7.5/picocli-4.7.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/jbig2-imageio/3.0.4/jbig2-imageio-3.0.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/lept4j/lept4j/1.18.2/lept4j-1.18.2.jar:/Users/<USER>/.m2/repository/org/jboss/jboss-vfs/3.2.17.Final/jboss-vfs-3.2.17.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar)
	at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:323)
	at com.sun.jna.NativeLibrary.getInstance(NativeLibrary.java:483)
	at com.sun.jna.Library$Handler.<init>(Library.java:197)
	at com.sun.jna.Native.load(Native.java:622)
	at com.sun.jna.Native.load(Native.java:596)
	at net.sourceforge.tess4j.util.LoadLibs.getTessAPIInstance(LoadLibs.java:83)
	at net.sourceforge.tess4j.TessAPI.<clinit>(TessAPI.java:42)
	at net.sourceforge.tess4j.Tesseract.init(Tesseract.java:518)
	at net.sourceforge.tess4j.Tesseract.doOCR(Tesseract.java:362)
	at net.sourceforge.tess4j.Tesseract.doOCR(Tesseract.java:311)
	at net.sourceforge.tess4j.Tesseract.doOCR(Tesseract.java:280)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.extractDiyCodeFromOCR(LabelProcessingServiceImpl.java:309)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.extractDiyCodeFromImage(LabelProcessingServiceImpl.java:260)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.processSingleImagePair(LabelProcessingServiceImpl.java:216)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.processZipArchive(LabelProcessingServiceImpl.java:89)
	at com.payne.server.banknote.controller.LabelProcessingController.processArchive(LabelProcessingController.java:68)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 128 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:211)
		... 154 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:224)
		... 154 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:268)
		... 154 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:268)
		... 154 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(/System/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache), '/Library/Frameworks/tesseract.framework/tesseract' (no such file)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:268)
		... 154 common frames omitted
	Suppressed: java.io.IOException: Native library (darwin-aarch64/libtesseract.dylib) not found in resource path (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/com/google/zxing/core/3.5.3/core-3.5.3.jar:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.5.3/javase-3.5.3.jar:/Users/<USER>/.m2/repository/com/beust/jcommander/1.82/jcommander-1.82.jar:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/tess4j/tess4j/5.9.0/tess4j-5.9.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox/3.0.1/pdfbox-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-io/3.0.1/pdfbox-io-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/fontbox/3.0.1/fontbox-3.0.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-tools/3.0.1/pdfbox-tools-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-debugger/3.0.1/pdfbox-debugger-3.0.1.jar:/Users/<USER>/.m2/repository/info/picocli/picocli/4.7.5/picocli-4.7.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/jbig2-imageio/3.0.4/jbig2-imageio-3.0.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/lept4j/lept4j/1.18.2/lept4j-1.18.2.jar:/Users/<USER>/.m2/repository/org/jboss/jboss-vfs/3.2.17.Final/jboss-vfs-3.2.17.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar)
		at com.sun.jna.Native.extractFromResourcePath(Native.java:1145)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:295)
		... 154 common frames omitted

[2m2025-08-10 22:32:30.090[0;39m [31mERROR[0;39m [35m42132[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-10 22:37:44.622[0;39m [31mERROR[0;39m [35m42132[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.core.handler.GlobalExceptionHandler [0;39m [2m:[0;39m Handler dispatch failed: java.lang.UnsatisfiedLinkError: Unable to load library 'tesseract':
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/System/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache), '/Library/Frameworks/tesseract.framework/tesseract' (no such file)
Native library (darwin-aarch64/libtesseract.dylib) not found in resource path (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/com/google/zxing/core/3.5.3/core-3.5.3.jar:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.5.3/javase-3.5.3.jar:/Users/<USER>/.m2/repository/com/beust/jcommander/1.82/jcommander-1.82.jar:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/tess4j/tess4j/5.9.0/tess4j-5.9.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox/3.0.1/pdfbox-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-io/3.0.1/pdfbox-io-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/fontbox/3.0.1/fontbox-3.0.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-tools/3.0.1/pdfbox-tools-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-debugger/3.0.1/pdfbox-debugger-3.0.1.jar:/Users/<USER>/.m2/repository/info/picocli/picocli/4.7.5/picocli-4.7.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/jbig2-imageio/3.0.4/jbig2-imageio-3.0.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/lept4j/lept4j/1.18.2/lept4j-1.18.2.jar:/Users/<USER>/.m2/repository/org/jboss/jboss-vfs/3.2.17.Final/jboss-vfs-3.2.17.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar)

jakarta.servlet.ServletException: Handler dispatch failed: java.lang.UnsatisfiedLinkError: Unable to load library 'tesseract':
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/System/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache), '/Library/Frameworks/tesseract.framework/tesseract' (no such file)
Native library (darwin-aarch64/libtesseract.dylib) not found in resource path (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/com/google/zxing/core/3.5.3/core-3.5.3.jar:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.5.3/javase-3.5.3.jar:/Users/<USER>/.m2/repository/com/beust/jcommander/1.82/jcommander-1.82.jar:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/tess4j/tess4j/5.9.0/tess4j-5.9.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox/3.0.1/pdfbox-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-io/3.0.1/pdfbox-io-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/fontbox/3.0.1/fontbox-3.0.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-tools/3.0.1/pdfbox-tools-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-debugger/3.0.1/pdfbox-debugger-3.0.1.jar:/Users/<USER>/.m2/repository/info/picocli/picocli/4.7.5/picocli-4.7.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/jbig2-imageio/3.0.4/jbig2-imageio-3.0.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/lept4j/lept4j/1.18.2/lept4j-1.18.2.jar:/Users/<USER>/.m2/repository/org/jboss/jboss-vfs/3.2.17.Final/jboss-vfs-3.2.17.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.payne.auth.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.UnsatisfiedLinkError: Unable to load library 'tesseract':
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/System/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache), '/Library/Frameworks/tesseract.framework/tesseract' (no such file)
Native library (darwin-aarch64/libtesseract.dylib) not found in resource path (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/com/google/zxing/core/3.5.3/core-3.5.3.jar:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.5.3/javase-3.5.3.jar:/Users/<USER>/.m2/repository/com/beust/jcommander/1.82/jcommander-1.82.jar:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/tess4j/tess4j/5.9.0/tess4j-5.9.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox/3.0.1/pdfbox-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-io/3.0.1/pdfbox-io-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/fontbox/3.0.1/fontbox-3.0.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-tools/3.0.1/pdfbox-tools-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-debugger/3.0.1/pdfbox-debugger-3.0.1.jar:/Users/<USER>/.m2/repository/info/picocli/picocli/4.7.5/picocli-4.7.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/jbig2-imageio/3.0.4/jbig2-imageio-3.0.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/lept4j/lept4j/1.18.2/lept4j-1.18.2.jar:/Users/<USER>/.m2/repository/org/jboss/jboss-vfs/3.2.17.Final/jboss-vfs-3.2.17.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar)
	at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:323)
	at com.sun.jna.NativeLibrary.getInstance(NativeLibrary.java:483)
	at com.sun.jna.Library$Handler.<init>(Library.java:197)
	at com.sun.jna.Native.load(Native.java:622)
	at com.sun.jna.Native.load(Native.java:596)
	at net.sourceforge.tess4j.util.LoadLibs.getTessAPIInstance(LoadLibs.java:83)
	at net.sourceforge.tess4j.TessAPI.<clinit>(TessAPI.java:42)
	at net.sourceforge.tess4j.Tesseract.init(Tesseract.java:518)
	at net.sourceforge.tess4j.Tesseract.doOCR(Tesseract.java:362)
	at net.sourceforge.tess4j.Tesseract.doOCR(Tesseract.java:311)
	at net.sourceforge.tess4j.Tesseract.doOCR(Tesseract.java:280)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.extractDiyCodeFromOCR(LabelProcessingServiceImpl.java:309)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.extractDiyCodeFromImage(LabelProcessingServiceImpl.java:260)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.processSingleImagePair(LabelProcessingServiceImpl.java:216)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.processZipArchive(LabelProcessingServiceImpl.java:89)
	at com.payne.server.banknote.controller.LabelProcessingController.processArchive(LabelProcessingController.java:68)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 128 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:211)
		... 154 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:224)
		... 154 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:268)
		... 154 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:268)
		... 154 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(/System/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache), '/Library/Frameworks/tesseract.framework/tesseract' (no such file)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:268)
		... 154 common frames omitted
	Suppressed: java.io.IOException: Native library (darwin-aarch64/libtesseract.dylib) not found in resource path (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/com/google/zxing/core/3.5.3/core-3.5.3.jar:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.5.3/javase-3.5.3.jar:/Users/<USER>/.m2/repository/com/beust/jcommander/1.82/jcommander-1.82.jar:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/tess4j/tess4j/5.9.0/tess4j-5.9.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox/3.0.1/pdfbox-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-io/3.0.1/pdfbox-io-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/fontbox/3.0.1/fontbox-3.0.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-tools/3.0.1/pdfbox-tools-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-debugger/3.0.1/pdfbox-debugger-3.0.1.jar:/Users/<USER>/.m2/repository/info/picocli/picocli/4.7.5/picocli-4.7.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/jbig2-imageio/3.0.4/jbig2-imageio-3.0.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/lept4j/lept4j/1.18.2/lept4j-1.18.2.jar:/Users/<USER>/.m2/repository/org/jboss/jboss-vfs/3.2.17.Final/jboss-vfs-3.2.17.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar)
		at com.sun.jna.Native.extractFromResourcePath(Native.java:1145)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:295)
		... 154 common frames omitted

[2m2025-08-10 22:49:53.728[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-10 22:50:27.401[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.core.handler.GlobalExceptionHandler [0;39m [2m:[0;39m Handler dispatch failed: java.lang.UnsatisfiedLinkError: Unable to load library 'tesseract':
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/System/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache), '/Library/Frameworks/tesseract.framework/tesseract' (no such file)
Native library (darwin-aarch64/libtesseract.dylib) not found in resource path (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/com/google/zxing/core/3.5.3/core-3.5.3.jar:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.5.3/javase-3.5.3.jar:/Users/<USER>/.m2/repository/com/beust/jcommander/1.82/jcommander-1.82.jar:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/tess4j/tess4j/5.9.0/tess4j-5.9.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox/3.0.1/pdfbox-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-io/3.0.1/pdfbox-io-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/fontbox/3.0.1/fontbox-3.0.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-tools/3.0.1/pdfbox-tools-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-debugger/3.0.1/pdfbox-debugger-3.0.1.jar:/Users/<USER>/.m2/repository/info/picocli/picocli/4.7.5/picocli-4.7.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/jbig2-imageio/3.0.4/jbig2-imageio-3.0.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/lept4j/lept4j/1.18.2/lept4j-1.18.2.jar:/Users/<USER>/.m2/repository/org/jboss/jboss-vfs/3.2.17.Final/jboss-vfs-3.2.17.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar)

jakarta.servlet.ServletException: Handler dispatch failed: java.lang.UnsatisfiedLinkError: Unable to load library 'tesseract':
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/System/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache), '/Library/Frameworks/tesseract.framework/tesseract' (no such file)
Native library (darwin-aarch64/libtesseract.dylib) not found in resource path (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/com/google/zxing/core/3.5.3/core-3.5.3.jar:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.5.3/javase-3.5.3.jar:/Users/<USER>/.m2/repository/com/beust/jcommander/1.82/jcommander-1.82.jar:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/tess4j/tess4j/5.9.0/tess4j-5.9.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox/3.0.1/pdfbox-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-io/3.0.1/pdfbox-io-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/fontbox/3.0.1/fontbox-3.0.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-tools/3.0.1/pdfbox-tools-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-debugger/3.0.1/pdfbox-debugger-3.0.1.jar:/Users/<USER>/.m2/repository/info/picocli/picocli/4.7.5/picocli-4.7.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/jbig2-imageio/3.0.4/jbig2-imageio-3.0.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/lept4j/lept4j/1.18.2/lept4j-1.18.2.jar:/Users/<USER>/.m2/repository/org/jboss/jboss-vfs/3.2.17.Final/jboss-vfs-3.2.17.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.payne.auth.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.UnsatisfiedLinkError: Unable to load library 'tesseract':
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/System/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache), '/Library/Frameworks/tesseract.framework/tesseract' (no such file)
Native library (darwin-aarch64/libtesseract.dylib) not found in resource path (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/com/google/zxing/core/3.5.3/core-3.5.3.jar:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.5.3/javase-3.5.3.jar:/Users/<USER>/.m2/repository/com/beust/jcommander/1.82/jcommander-1.82.jar:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/tess4j/tess4j/5.9.0/tess4j-5.9.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox/3.0.1/pdfbox-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-io/3.0.1/pdfbox-io-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/fontbox/3.0.1/fontbox-3.0.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-tools/3.0.1/pdfbox-tools-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-debugger/3.0.1/pdfbox-debugger-3.0.1.jar:/Users/<USER>/.m2/repository/info/picocli/picocli/4.7.5/picocli-4.7.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/jbig2-imageio/3.0.4/jbig2-imageio-3.0.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/lept4j/lept4j/1.18.2/lept4j-1.18.2.jar:/Users/<USER>/.m2/repository/org/jboss/jboss-vfs/3.2.17.Final/jboss-vfs-3.2.17.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar)
	at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:323)
	at com.sun.jna.NativeLibrary.getInstance(NativeLibrary.java:483)
	at com.sun.jna.Library$Handler.<init>(Library.java:197)
	at com.sun.jna.Native.load(Native.java:622)
	at com.sun.jna.Native.load(Native.java:596)
	at net.sourceforge.tess4j.util.LoadLibs.getTessAPIInstance(LoadLibs.java:83)
	at net.sourceforge.tess4j.TessAPI.<clinit>(TessAPI.java:42)
	at net.sourceforge.tess4j.Tesseract.init(Tesseract.java:518)
	at net.sourceforge.tess4j.Tesseract.doOCR(Tesseract.java:362)
	at net.sourceforge.tess4j.Tesseract.doOCR(Tesseract.java:311)
	at net.sourceforge.tess4j.Tesseract.doOCR(Tesseract.java:280)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.extractDiyCodeFromOCR(LabelProcessingServiceImpl.java:309)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.extractDiyCodeFromImage(LabelProcessingServiceImpl.java:260)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.processSingleImagePair(LabelProcessingServiceImpl.java:216)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.processZipArchive(LabelProcessingServiceImpl.java:89)
	at com.payne.server.banknote.controller.LabelProcessingController.processArchive(LabelProcessingController.java:68)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 128 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:211)
		... 154 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:224)
		... 154 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:268)
		... 154 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:268)
		... 154 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: dlopen(/System/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/System/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache), '/Library/Frameworks/tesseract.framework/tesseract' (no such file)
		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:268)
		... 154 common frames omitted
	Suppressed: java.io.IOException: Native library (darwin-aarch64/libtesseract.dylib) not found in resource path (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/com/google/zxing/core/3.5.3/core-3.5.3.jar:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.5.3/javase-3.5.3.jar:/Users/<USER>/.m2/repository/com/beust/jcommander/1.82/jcommander-1.82.jar:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/tess4j/tess4j/5.9.0/tess4j-5.9.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox/3.0.1/pdfbox-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-io/3.0.1/pdfbox-io-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/fontbox/3.0.1/fontbox-3.0.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-tools/3.0.1/pdfbox-tools-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-debugger/3.0.1/pdfbox-debugger-3.0.1.jar:/Users/<USER>/.m2/repository/info/picocli/picocli/4.7.5/picocli-4.7.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/jbig2-imageio/3.0.4/jbig2-imageio-3.0.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/lept4j/lept4j/1.18.2/lept4j-1.18.2.jar:/Users/<USER>/.m2/repository/org/jboss/jboss-vfs/3.2.17.Final/jboss-vfs-3.2.17.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar)
		at com.sun.jna.Native.extractFromResourcePath(Native.java:1145)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:295)
		... 154 common frames omitted

[2m2025-08-10 22:55:08.067[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.core.handler.GlobalExceptionHandler [0;39m [2m:[0;39m Handler dispatch failed: java.lang.NoClassDefFoundError: Could not initialize class net.sourceforge.tess4j.TessAPI

jakarta.servlet.ServletException: Handler dispatch failed: java.lang.NoClassDefFoundError: Could not initialize class net.sourceforge.tess4j.TessAPI
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.payne.auth.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NoClassDefFoundError: Could not initialize class net.sourceforge.tess4j.TessAPI
	at net.sourceforge.tess4j.Tesseract.init(Tesseract.java:518)
	at net.sourceforge.tess4j.Tesseract.doOCR(Tesseract.java:362)
	at net.sourceforge.tess4j.Tesseract.doOCR(Tesseract.java:311)
	at net.sourceforge.tess4j.Tesseract.doOCR(Tesseract.java:280)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.extractDiyCodeFromOCR(LabelProcessingServiceImpl.java:309)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.extractDiyCodeFromImage(LabelProcessingServiceImpl.java:260)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.processSingleImagePair(LabelProcessingServiceImpl.java:216)
	at com.payne.server.banknote.service.impl.LabelProcessingServiceImpl.processZipArchive(LabelProcessingServiceImpl.java:89)
	at com.payne.server.banknote.controller.LabelProcessingController.processArchive(LabelProcessingController.java:68)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 128 common frames omitted
Caused by: java.lang.ExceptionInInitializerError: Exception java.lang.UnsatisfiedLinkError: Unable to load library 'tesseract':
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(libtesseract.dylib, 0x0009): tried: 'libtesseract.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/./libtesseract.dylib' (no such file), '/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/../lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache), 'libtesseract.dylib' (no such file), '/usr/local/lib/libtesseract.dylib' (no such file), '/usr/lib/libtesseract.dylib' (no such file, not in dyld cache)
dlopen(/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Users/<USER>/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Library/Frameworks/tesseract.framework/tesseract' (no such file, not in dyld cache)
dlopen(/Library/Frameworks/tesseract.framework/tesseract, 0x0009): tried: '/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/Library/Frameworks/tesseract.framework/tesseract' (no such file), '/Library/Frameworks/t
	at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:323)
	at com.sun.jna.NativeLibrary.getInstance(NativeLibrary.java:483)
	at com.sun.jna.Library$Handler.<init>(Library.java:197)
	at com.sun.jna.Native.load(Native.java:622)
	at com.sun.jna.Native.load(Native.java:596)
	at net.sourceforge.tess4j.util.LoadLibs.getTessAPIInstance(LoadLibs.java:83)
	at net.sourceforge.tess4j.TessAPI.<clinit>(TessAPI.java:42)
	... 148 common frames omitted

[2m2025-08-11 04:00:51.861[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[isson-netty-1-8][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xcc133f0b, L:/192.168.8.92:59931 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:51.861[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[isson-netty-1-7][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x39759110, L:/192.168.8.92:59932 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:51.965[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-10][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x3eed2c7b, L:/192.168.8.92:59934 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:51.965[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[isson-netty-1-9][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x91330788, L:/192.168.8.92:59933 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:52.064[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-12][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x2b801a84, L:/192.168.8.92:59936 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:52.064[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-11][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x511ebde2, L:/192.168.8.92:59937 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:52.171[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-14][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xadc10e19, L:/192.168.8.92:59939 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:52.171[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-13][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x36948f43, L:/192.168.8.92:59940 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:52.268[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-16][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x0e6aac19, L:/192.168.8.92:59941 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:52.268[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-15][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xf6cddb2e, L:/192.168.8.92:59942 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:52.369[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-18][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x4a48a79b, L:/192.168.8.92:59951 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:52.369[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-17][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x3659898c, L:/192.168.8.92:59950 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:52.570[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-21][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x7907f3eb, L:/192.168.8.92:59963 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:52.570[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-22][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xe9d99cf4, L:/192.168.8.92:59964 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:52.656[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-24][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x443c2db3, L:/192.168.8.92:59967 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:52.656[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-23][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x48be8193, L:/192.168.8.92:59966 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:52.768[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-26][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xe0354e8c, L:/192.168.8.92:59972 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.buffer.WrappedByteBuf.writeBytes(WrappedByteBuf.java:821)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:00:52.769[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-25][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x88b4caf8, L:/192.168.8.92:59971 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:01:03.917[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0xf1b2e876, L:/192.168.8.92:59926 - R:paynexc.home/************:6379]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://paynexc.home:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:01:03.922[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x64f639dd, L:/192.168.8.92:59927 - R:paynexc.home/************:6379]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://paynexc.home:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:01:03.926[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0xc01bb37e, L:/192.168.8.92:59928 - R:paynexc.home/************:6379]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://paynexc.home:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:01:04.619[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x7f1c44a2, L:/192.168.8.92:59955 - R:paynexc.home/************:6379]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://paynexc.home:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:01:04.621[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x9b4c5b56, L:/192.168.8.92:59956 - R:paynexc.home/************:6379]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://paynexc.home:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:01:05.010[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x72d24c87, L:/192.168.8.92:59975 - R:paynexc.home/************:6379]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://paynexc.home:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 04:01:05.012[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x9e964541, L:/192.168.8.92:59974 - R:paynexc.home/************:6379]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://paynexc.home:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 07:53:46.043[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-28][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xe986ba3b, L:/192.168.8.92:50752 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 07:53:46.043[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-29][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x1be8fa5a, L:/192.168.8.92:50753 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 07:53:46.216[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-30][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xd1198463, L:/192.168.8.92:50755 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 07:53:46.217[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-31][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xb12b9785, L:/192.168.8.92:50756 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 07:53:46.231[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-32][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xc24d5dc3, L:/192.168.8.92:50759 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 07:53:46.231[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[isson-netty-1-1][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x099372b1, L:/192.168.8.92:50758 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 07:53:46.356[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[isson-netty-1-3][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xb497339c, L:/192.168.8.92:50761 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 07:53:46.356[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[isson-netty-1-2][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x6a115ff1, L:/192.168.8.92:50760 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 07:53:48.434[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[isson-netty-1-4][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x78f3ffe3, L:/192.168.8.92:50794 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:33.323[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-20][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x7c6af48c, L:/192.168.8.92:50739 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:33.323[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-19][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x0be83dc1, L:/192.168.8.92:50738 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:33.421[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-22][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x27fa0d36, L:/192.168.8.92:50744 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:33.520[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-24][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x6919810a, L:/192.168.8.92:50748 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:33.520[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-25][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xabd4f9dd, L:/192.168.8.92:50747 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:33.623[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-27][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xc337f5b5, L:/192.168.8.92:50749 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:33.623[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-26][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xd452c3ef, L:/192.168.8.92:50750 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:33.781[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-23][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x22691a43, L:/192.168.8.92:50745 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:33.958[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-15][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x0ae4c34b, L:/192.168.8.92:50722 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:34.153[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-12][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x3eb35c3d, L:/192.168.8.92:50707 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:34.194[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-17][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xa1f12f2d, L:/192.168.8.92:50735 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:34.194[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-18][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x56c592c2, L:/192.168.8.92:50736 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:34.880[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-16][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x03935803, L:/192.168.8.92:50721 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:35.160[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-13][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xa5f6b4d5, L:/192.168.8.92:50717 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:09:35.187[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-14][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0xa94a4d76, L:/192.168.8.92:50716 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 08:18:23.163[0;39m [31mERROR[0;39m [35m46645[0;39m [2m---[0;39m [2m[sson-netty-1-11][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x0cc4ddd2, L:/192.168.8.92:50706 - R:paynexc.home/************:6379]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-11 16:57:57.381[0;39m [31mERROR[0;39m [35m12278[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-11 16:58:42.358[0;39m [31mERROR[0;39m [35m12278[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.web.embedded.tomcat.TomcatStarter [0;39m [2m:[0;39m Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'jwtAuthenticationFilter' defined in class path resource [com/payne/auth/security/SecurityConfig.class]: Unsatisfied dependency expressed through method 'jwtAuthenticationFilter' parameter 3: Error creating bean with name 'redisTemplate' defined in class path resource [com/payne/core/config/RedisConfig.class]: Unsatisfied dependency expressed through method 'redisTemplate' parameter 0: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfigurationV2.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfigurationV2.class]: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception with message: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: paynexc.home/************:6379
[2m2025-08-11 16:58:42.717[0;39m [31mERROR[0;39m [35m12278[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.SpringApplication              [0;39m [2m:[0;39m Application run failed

org.springframework.context.ApplicationContextException: Unable to start web server
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext.__refresh(AbstractApplicationContext.java:621)
	at org.springframework.context.support.AbstractApplicationContext.jrLockAndRefresh(AbstractApplicationContext.java:41002)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:42008)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.payne.App.main(App.java:20)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:147)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:107)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:517)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:219)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:193)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:167)
	... 10 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'jwtAuthenticationFilter' defined in class path resource [com/payne/auth/security/SecurityConfig.class]: Unsatisfied dependency expressed through method 'jwtAuthenticationFilter' parameter 3: Error creating bean with name 'redisTemplate' defined in class path resource [com/payne/core/config/RedisConfig.class]: Unsatisfied dependency expressed through method 'redisTemplate' parameter 0: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfigurationV2.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfigurationV2.class]: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception with message: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: paynexc.home/************:6379
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:546)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:230)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:184)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:179)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:164)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:96)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:271)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:245)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:52)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4464)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:772)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:203)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:870)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:438)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:128)
	... 15 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redisTemplate' defined in class path resource [com/payne/core/config/RedisConfig.class]: Unsatisfied dependency expressed through method 'redisTemplate' parameter 0: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfigurationV2.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfigurationV2.class]: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception with message: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: paynexc.home/************:6379
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:546)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1683)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 56 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfigurationV2.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfigurationV2.class]: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception with message: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: paynexc.home/************:6379
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:546)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1745)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 69 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfigurationV2.class]: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception with message: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: paynexc.home/************:6379
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:489)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1745)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 83 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception with message: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: paynexc.home/************:6379
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 97 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: paynexc.home/************:6379
	at org.redisson.connection.MasterSlaveConnectionManager.doConnect(MasterSlaveConnectionManager.java:228)
	at org.redisson.connection.MasterSlaveConnectionManager.connect(MasterSlaveConnectionManager.java:188)
	at org.redisson.config.ConfigSupport.createConnectionManager(ConfigSupport.java:216)
	at org.redisson.Redisson.<init>(Redisson.java:70)
	at org.redisson.Redisson.create(Redisson.java:115)
	at org.redisson.spring.starter.RedissonAutoConfiguration.redisson(RedissonAutoConfiguration.java:331)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	... 100 common frames omitted
Caused by: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: paynexc.home/************:6379
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at org.redisson.connection.MasterSlaveConnectionManager.doConnect(MasterSlaveConnectionManager.java:223)
	... 110 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: paynexc.home/************:6379
	at org.redisson.connection.ConnectionsHolder.lambda$createConnection$2(ConnectionsHolder.java:165)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2162)
	at org.redisson.connection.ConnectionsHolder.lambda$createConnection$5(ConnectionsHolder.java:179)
	at java.base/java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:863)
	at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:841)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2162)
	at org.redisson.client.RedisClient$1$2.run(RedisClient.java:304)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.util.concurrent.CompletionException: io.netty.channel.ConnectTimeoutException: connection timed out after 10000 ms: paynexc.home/************:6379
	at java.base/java.util.concurrent.CompletableFuture.encodeRelay(CompletableFuture.java:368)
	at java.base/java.util.concurrent.CompletableFuture.completeRelay(CompletableFuture.java:377)
	at java.base/java.util.concurrent.CompletableFuture$UniRelay.tryFire(CompletableFuture.java:1097)
	... 12 common frames omitted
Caused by: io.netty.channel.ConnectTimeoutException: connection timed out after 10000 ms: paynexc.home/************:6379
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:156)
	... 9 common frames omitted

